"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "video-complete",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const videoSrc = common_vendor.ref("");
    const videoPoster = common_vendor.ref("");
    const orderNo = common_vendor.ref("");
    const getVideoDetail = async () => {
      try {
        const res = await api_index.getVideoDetailApi({
          merchantGuid: userStore.merchantGuid,
          orderNo: orderNo.value
        });
        if (res.code === 0) {
          const { videoUrl, previewUrl } = res.data;
          if (videoUrl) {
            videoSrc.value = videoUrl;
          }
          if (previewUrl) {
            videoPoster.value = previewUrl;
          }
        } else {
          common_vendor.index.__f__("error", "at pages/my/video-complete.vue:69", "获取视频详情失败:", res.msg);
          common_vendor.index.showToast({
            title: "获取视频失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/video-complete.vue:76", "获取视频详情失败:", error);
        common_vendor.index.showToast({
          title: "获取视频失败",
          icon: "none"
        });
      }
    };
    const handleSave = async () => {
      if (!videoSrc.value) {
        common_vendor.index.showToast({
          title: "视频还未加载完成",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      try {
        const downloadResult = await new Promise((resolve, reject) => {
          common_vendor.index.downloadFile({
            url: videoSrc.value,
            success: (res) => {
              if (res.statusCode === 200) {
                resolve(res.tempFilePath);
              } else {
                reject(new Error("下载失败"));
              }
            },
            fail: (err) => {
              reject(err);
            }
          });
        });
        await new Promise((resolve, reject) => {
          common_vendor.index.saveVideoToPhotosAlbum({
            filePath: downloadResult,
            success: () => {
              resolve();
            },
            fail: (err) => {
              if (err.errMsg.includes("auth")) {
                common_vendor.index.showModal({
                  title: "提示",
                  content: "需要您授权访问相册才能保存视频，请在设置中开启相册权限",
                  showCancel: false
                });
              }
              reject(err);
            }
          });
        });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/my/video-complete.vue:147", "保存视频失败:", error);
        common_vendor.index.showToast({
          title: "保存失败，请重试",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const onShareAppMessage = () => {
      return {
        title: "我制作了一个精彩的视频，快来看看吧！",
        path: `/pages/my/video-complete?orderNo=${orderNo.value}`,
        imageUrl: videoPoster.value || ""
      };
    };
    common_vendor.onLoad((options) => {
      if (options.orderNo) {
        orderNo.value = options.orderNo;
        getVideoDetail();
      } else {
        videoSrc.value = "https://vd3.bdstatic.com/mda-ka0x6301f525mw5e/mda-ka0x6301f525mw5e.mp4?playlist=%5B%22hd%22%2C%22sc%22%5D";
      }
    });
    common_vendor.onMounted(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: videoSrc.value,
        b: videoPoster.value,
        c: common_assets._imports_0$7,
        d: common_vendor.o(handleSave),
        e: common_assets._imports_1$4,
        f: common_vendor.o(onShareAppMessage)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f5ae4b12"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/video-complete.js.map
