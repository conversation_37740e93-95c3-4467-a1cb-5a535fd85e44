"use strict";
const common_vendor = require("../../common/vendor.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "secret",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const secretKeyList = common_vendor.ref([]);
    const getSecretKeyList = async () => {
      try {
        let res = await api_index.getSecretKeyListApi({
          merchantGuid: userStore.merchantGuid
        });
        if (res.code === 0 && res.data && res.data.length > 0) {
          secretKeyList.value = res.data.map((item) => ({
            ...item,
            secretKey: item.secretKey || ""
            // 如果没有secretKey则设为空字符串
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/secret.vue:48", "获取秘钥列表失败:", error);
        common_vendor.index.showToast({
          title: "获取秘钥失败",
          icon: "none"
        });
      }
    };
    const saving = common_vendor.ref(false);
    const handleSave = async () => {
      const hasValidKey = secretKeyList.value.some((item) => item.secretKey && item.secretKey.trim());
      if (!hasValidKey) {
        common_vendor.index.showToast({
          title: "请至少输入一个秘钥",
          icon: "none"
        });
        return;
      }
      saving.value = true;
      try {
        const saveSecretKeyList = secretKeyList.value.filter((item) => item.secretKey && item.secretKey.trim()).map((item) => ({
          secretKeyType: item.secretKeyType,
          secretKey: item.secretKey.trim()
        }));
        const saveData = {
          merchantGuid: userStore.merchantGuid,
          saveSecretKeyList
        };
        let res = await api_index.saveSecretKeyApi(saveData);
        if (res.code === 0) {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          common_vendor.index.showToast({
            title: res.msg || "保存失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/create-agent/secret.vue:106", "保存秘钥失败:", error);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "none"
        });
      } finally {
        saving.value = false;
      }
    };
    common_vendor.onMounted(() => {
      getSecretKeyList();
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(secretKeyList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.secretKeyTypeText),
            b: `请输入${item.secretKeyTypeText}`,
            c: item.secretKey,
            d: common_vendor.o(($event) => item.secretKey = $event.detail.value, item.secretKeyType),
            e: item.secretKeyType
          };
        }),
        b: common_vendor.t(saving.value ? "保存中..." : "保存"),
        c: saving.value ? 1 : "",
        d: common_vendor.o(handleSave)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-358c5fc5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/create-agent/secret.js.map
