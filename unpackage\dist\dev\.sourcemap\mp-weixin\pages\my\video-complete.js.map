{"version": 3, "file": "video-complete.js", "sources": ["pages/my/video-complete.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXkvdmlkZW8tY29tcGxldGUudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"video-complete-page\">\r\n\t\t<!-- 视频播放区域 -->\r\n\t\t<view class=\"video-container\">\r\n\t\t\t<video \r\n\t\t\t\tclass=\"video-player\"\r\n\t\t\t\t:src=\"videoSrc\"\r\n\t\t\t\t:poster=\"videoPoster\"\r\n\t\t\t\t:autoplay=\"true\"\r\n\t\t\t\tcontrols\r\n\t\t\t\t:show-fullscreen-btn=\"true\"\r\n\t\t\t\t:show-play-btn=\"true\"\r\n\t\t\t\t:show-center-play-btn=\"true\"\r\n\t\t\t\t:enable-progress-gesture=\"true\"\r\n\t\t\t\t:object-fit=\"'contain'\"\r\n\t\t\t>\r\n\t\t\t</video>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<view class=\"action-btn save-btn\" @tap=\"handleSave\">\r\n\t\t\t\t<image class=\"action-icon\" src=\"/static/my/video_download.png\" mode=\"aspectFit\" />\r\n\t\t\t\t<text class=\"action-text\">保存作品</text>\r\n\t\t\t</view>\r\n\t\t\t<button\r\n\t\t\t\tclass=\"action-btn share-btn\"\r\n\t\t\t\topen-type=\"share\"\r\n\t\t\t\t@getuserinfo=\"onShareAppMessage\"\r\n\t\t\t>\r\n\t\t\t\t<image class=\"action-icon\" src=\"/static/my/video_share.png\" mode=\"aspectFit\" />\r\n\t\t\t\t<text class=\"action-text\">分享好友</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport { getVideoDetailApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\n// 用户store\r\nconst userStore = useUserStore()\r\n\r\n// 视频相关数据\r\nconst videoSrc = ref('')\r\nconst videoPoster = ref('')\r\nconst orderNo = ref('')\r\n\r\n// 获取视频详情\r\nconst getVideoDetail = async () => {\r\n\ttry {\r\n\t\tconst res = await getVideoDetailApi({\r\n\t\t\tmerchantGuid: userStore.merchantGuid,\r\n\t\t\torderNo: orderNo.value\r\n\t\t})\r\n\r\n\t\tif (res.code === 0) {\r\n\t\t\tconst { videoUrl, previewUrl } = res.data\r\n\t\t\tif (videoUrl) {\r\n\t\t\t\tvideoSrc.value = videoUrl\r\n\t\t\t}\r\n\t\t\tif (previewUrl) {\r\n\t\t\t\tvideoPoster.value = previewUrl\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.error('获取视频详情失败:', res.msg)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '获取视频失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('获取视频详情失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '获取视频失败',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 处理保存作品\r\nconst handleSave = async () => {\r\n\tif (!videoSrc.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '视频还未加载完成',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn\r\n\t}\r\n\r\n\t// 显示加载提示\r\n\tuni.showLoading({\r\n\t\ttitle: '正在保存...'\r\n\t})\r\n\r\n\ttry {\r\n\t\t// 下载视频文件\r\n\t\tconst downloadResult = await new Promise((resolve, reject) => {\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: videoSrc.value,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tresolve(res.tempFilePath)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treject(new Error('下载失败'))\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\treject(err)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\r\n\t\t// 保存视频到相册\r\n\t\tawait new Promise((resolve, reject) => {\r\n\t\t\tuni.saveVideoToPhotosAlbum({\r\n\t\t\t\tfilePath: downloadResult,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tresolve()\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t// 如果是权限问题，提示用户授权\r\n\t\t\t\t\tif (err.errMsg.includes('auth')) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\tcontent: '需要您授权访问相册才能保存视频，请在设置中开启相册权限',\r\n\t\t\t\t\t\t\tshowCancel: false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\treject(err)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t})\r\n\r\n\t\tuni.hideLoading()\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '保存成功',\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\r\n\t} catch (error) {\r\n\t\tuni.hideLoading()\r\n\t\tconsole.error('保存视频失败:', error)\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '保存失败，请重试',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t})\r\n\t}\r\n}\r\n\r\n// 分享配置\r\nconst onShareAppMessage = () => {\r\n\treturn {\r\n\t\ttitle: '我制作了一个精彩的视频，快来看看吧！',\r\n\t\tpath: `/pages/my/video-complete?orderNo=${orderNo.value}`,\r\n\t\timageUrl: videoPoster.value || ''\r\n\t}\r\n}\r\n\r\n// 使用onLoad获取页面参数\r\nonLoad((options) => {\r\n\t// getVideoDetail()\r\n\t// return\r\n\r\n\tif (options.orderNo) {\r\n\t\torderNo.value = options.orderNo\r\n\t\t// 获取视频详情\r\n\t\tgetVideoDetail()\r\n\t} else {\r\n\t\t// 如果没有orderNo参数，使用示例数据\r\n\t\tvideoSrc.value = 'https://vd3.bdstatic.com/mda-ka0x6301f525mw5e/mda-ka0x6301f525mw5e.mp4?playlist=%5B%22hd%22%2C%22sc%22%5D'\r\n\t}\r\n})\r\n\r\nonMounted(() => {\r\n\t// 页面挂载时的其他初始化操作\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.video-complete-page {\r\n\tbackground: #F5F5F5;\r\n\tmin-height: 100vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tpadding: 0;\r\n\tbox-sizing: border-box;\r\n\r\n\t.video-container {\r\n\t\theight: calc(100vh - 180rpx);\r\n\t\tbackground: #000000;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.video-player {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground: #000000;\r\n\t\t}\r\n\t}\r\n\r\n\t.bottom-actions {\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 0 30rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\theight: 180rpx;\r\n\r\n\t\t.action-btn {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\twidth: 330rpx;\r\n\t\t\theight: 90rpx;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tborder-radius: 50rpx;\r\n\t\t\tborder: none;\r\n\t\t\tpadding: 0;\r\n\t\t\tmargin: 0;\r\n\t\t\tline-height: normal;\r\n\r\n\t\t\t&:active {\r\n\t\t\t\topacity: 0.8;\r\n\t\t\t}\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tborder: none;\r\n\t\t\t}\r\n\r\n\t\t\t.action-icon {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tmargin-right: 14rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.action-text {\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t}\r\n\r\n\t\t\t&.save-btn {\r\n\t\t\t\tbackground: #ECECEC;\r\n\t\t\t\tborder: 2rpx solid #E9ECEF;\r\n\r\n\t\t\t\t.action-text {\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t&.share-btn {\r\n\t\t\t\tbackground: #2A64F6;\r\n\r\n\t\t\t\t.action-text {\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/my/video-complete.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "getVideoDetailApi", "uni", "onLoad", "onMounted"], "mappings": ";;;;;;;;AA4CA,UAAA,YAAAA,YAAAA,aAAA;AAGA,UAAA,WAAAC,cAAA,IAAA,EAAA;AACA,UAAA,cAAAA,cAAA,IAAA,EAAA;AACA,UAAA,UAAAA,cAAA,IAAA,EAAA;AAGA,UAAA,iBAAA,YAAA;AACA,UAAA;AACA,cAAA,MAAA,MAAAC,4BAAA;AAAA,UACA,cAAA,UAAA;AAAA,UACA,SAAA,QAAA;AAAA,QACA,CAAA;AAEA,YAAA,IAAA,SAAA,GAAA;AACA,gBAAA,EAAA,UAAA,WAAA,IAAA,IAAA;AACA,cAAA,UAAA;AACA,qBAAA,QAAA;AAAA,UACA;AACA,cAAA,YAAA;AACA,wBAAA,QAAA;AAAA,UACA;AAAA,QACA,OAAA;AACAC,wBAAA,MAAA,MAAA,SAAA,qCAAA,aAAA,IAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACA,OAAA;AAAA,YACA,MAAA;AAAA,UACA,CAAA;AAAA,QACA;AAAA,MACA,SAAA,OAAA;AACAA,sBAAAA,MAAA,MAAA,SAAA,qCAAA,aAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,aAAA,YAAA;AACA,UAAA,CAAA,SAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,QACA,CAAA;AACA;AAAA,MACA;AAGAA,oBAAAA,MAAA,YAAA;AAAA,QACA,OAAA;AAAA,MACA,CAAA;AAEA,UAAA;AAEA,cAAA,iBAAA,MAAA,IAAA,QAAA,CAAA,SAAA,WAAA;AACAA,wBAAAA,MAAA,aAAA;AAAA,YACA,KAAA,SAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACA,kBAAA,IAAA,eAAA,KAAA;AACA,wBAAA,IAAA,YAAA;AAAA,cACA,OAAA;AACA,uBAAA,IAAA,MAAA,MAAA,CAAA;AAAA,cACA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AACA,qBAAA,GAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,CAAA;AAGA,cAAA,IAAA,QAAA,CAAA,SAAA,WAAA;AACAA,wBAAAA,MAAA,uBAAA;AAAA,YACA,UAAA;AAAA,YACA,SAAA,MAAA;AACA,sBAAA;AAAA,YACA;AAAA,YACA,MAAA,CAAA,QAAA;AAEA,kBAAA,IAAA,OAAA,SAAA,MAAA,GAAA;AACAA,8BAAAA,MAAA,UAAA;AAAA,kBACA,OAAA;AAAA,kBACA,SAAA;AAAA,kBACA,YAAA;AAAA,gBACA,CAAA;AAAA,cACA;AACA,qBAAA,GAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,CAAA;AAEAA,sBAAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAAA,MAEA,SAAA,OAAA;AACAA,sBAAAA,MAAA,YAAA;AACAA,sBAAAA,MAAA,MAAA,SAAA,sCAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACA,OAAA;AAAA,UACA,MAAA;AAAA,UACA,UAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,oBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA,oCAAA,QAAA,KAAA;AAAA,QACA,UAAA,YAAA,SAAA;AAAA,MACA;AAAA,IACA;AAGAC,kBAAA,OAAA,CAAA,YAAA;AAIA,UAAA,QAAA,SAAA;AACA,gBAAA,QAAA,QAAA;AAEA,uBAAA;AAAA,MACA,OAAA;AAEA,iBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEAC,kBAAAA,UAAA,MAAA;AAAA,IAEA,CAAA;;;;;;;;;;;;;;;ACpLA,GAAG,WAAW,eAAe;"}