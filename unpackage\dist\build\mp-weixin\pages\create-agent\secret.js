"use strict";const e=require("../../common/vendor.js"),t=require("../../api/index.js"),s=require("../../stores/user.js"),r={__name:"secret",setup(r){const c=s.useUserStore(),a=e.ref([]),o=e.ref(!1),i=async()=>{if(a.value.some((e=>e.secretKey&&e.secretKey.trim()))){o.value=!0;try{const s=a.value.filter((e=>e.secretKey&&e.secretKey.trim())).map((e=>({secretKeyType:e.secretKeyType,secretKey:e.secretKey.trim()}))),r={merchantGuid:c.merchantGuid,saveSecretKeyList:s};let i=await t.saveSecretKeyApi(r);0===i.code?(e.index.showToast({title:"保存成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)):e.index.showToast({title:i.msg||"保存失败",icon:"none"})}catch(s){console.error("保存秘钥失败:",s),e.index.showToast({title:"保存失败",icon:"none"})}finally{o.value=!1}}else e.index.showToast({title:"请至少输入一个秘钥",icon:"none"})};return e.onMounted((()=>{(async()=>{try{let e=await t.getSecretKeyListApi({merchantGuid:c.merchantGuid});0===e.code&&e.data&&e.data.length>0&&(a.value=e.data.map((e=>({...e,secretKey:e.secretKey||""}))))}catch(s){console.error("获取秘钥列表失败:",s),e.index.showToast({title:"获取秘钥失败",icon:"none"})}})()})),(t,s)=>({a:e.f(a.value,((t,s,r)=>({a:e.t(t.secretKeyTypeText),b:`请输入${t.secretKeyTypeText}`,c:t.secretKey,d:e.o((e=>t.secretKey=e.detail.value),t.secretKeyType),e:t.secretKeyType}))),b:e.t(o.value?"保存中...":"保存"),c:o.value?1:"",d:e.o(i)})}},c=e._export_sfc(r,[["__scopeId","data-v-b22a19c6"]]);wx.createPage(c);
