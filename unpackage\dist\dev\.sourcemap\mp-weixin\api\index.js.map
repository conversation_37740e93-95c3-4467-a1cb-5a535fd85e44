{"version": 3, "file": "index.js", "sources": ["api/index.js"], "sourcesContent": ["import request from \"@/request/request.js\";\r\n// import config from '@/config/config.js'\r\n\r\n//小程序静默登录\r\nexport const mpLoginApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.user/xcxSilenceLogin',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//更新用户默认数据\r\nexport const updateUserDefaultProfileApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentSquare/updateUserDefaultProfile',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 小程序手机登录\r\nexport const phoneAuthLoginApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.user/phoneAuthLogin',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 获取默认配置\r\nexport const showConfigsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'zhanhui/api.index/showConfigs',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取首页数据\r\nexport const getHomeDataApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentPublic/homeData',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取智能体分类列表\r\nexport const getCategoryListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentPublic/categoryList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取会话列表\r\nexport const getMySessionListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/mySessionList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//通过分类获取智能体列表\r\nexport const getAgentListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentSquare/agentList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//订阅智能体\r\nexport const subscribeAgentApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentSquare/subscribeAgent',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//修改会话标题\r\nexport const updateSessionTitleApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/updateSessionTitle',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//删除会话\r\nexport const deleteSessionApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/deleteSession',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//修改用户信息\r\nexport const updateUserInfoApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userinfo/update',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取用户信息\r\nexport const getUserInfoApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userinfo/index',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取我的智能体列表\r\nexport const getMyAgentListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/myList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//创建智能体\r\nexport const createAgentApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/create',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//Ai生成头像\r\nexport const generateAvatarApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/generateAvatar',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取我的智能体详情\r\nexport const getMyDetailApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/myDetail',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//修改我的智能体\r\nexport const updateMyAgentApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/update',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取会话历史消息\r\nexport const getMessageHistoryApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/messageHistory',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取会话历史消息\r\nexport const saveMsgApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/saveMsg',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取规则信息\r\nexport const platformRulesApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentPublic/platformRules',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取智能体详情\r\nexport const agentDetailApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/agentDetail',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//删除会话所有消息记录\r\nexport const deleteAllMessagesApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/deleteAllMessages',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//删除单条消息记录\r\nexport const deleteMessageApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/deleteMessage',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//收藏消息\r\nexport const collectMessageApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/collectMessage',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//取消收藏消息\r\nexport const cancelCollectMessageApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/uncollectMessage',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取我的收藏列表\r\nexport const getMyCollectionListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/myCollectionList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//创建购买订单\r\nexport const createPurchaseOrderApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentSquare/createPurchaseOrder',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//查询购买订单\r\nexport const queryPurchaseOrderApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentSquare/queryPurchaseOrder',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//创建会员套餐订阅订单\r\nexport const createVipOrderApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentMembership/purchasePackage',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取会员套餐列表\r\nexport const getVipPackageListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentMembership/packageList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取用户会员信息\r\nexport const getUserVipInfoApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentMembership/myMembership',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//查询会员订阅订单\r\nexport const queryVipOrderApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentMembership/queryPayment',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n\r\n//获取邀请码\r\nexport const getInvitationCodeApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userinfo/getInvitationCode',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//绑定邀请码\r\nexport const bindInvitationApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/bindInvitation',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//生成小程序码\r\nexport const generateMiniCodeApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/generateMiniCode',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取订阅列表\r\nexport const getSubscriptionListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentCreatorSubscription/creatorList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//订阅智能体创作者\r\nexport const subscribeCreatorApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentCreatorSubscription/purchaseCreatorSubscription',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//查询订阅订单\r\nexport const querySubscriptionOrderApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentCreatorSubscription/queryPayment',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取用户订阅列表\r\nexport const getMySubscriptionListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentCreatorSubscription/myCreatorSubscriptions',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取订阅规则信息\r\nexport const getSubscriptionRuleApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentMembership/subscriptionRule',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//获取banner图\r\nexport const showBannerUrlsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentPublic/showBannerUrls',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//设置会话置顶\r\nexport const setSessionTopApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentChat/setSessionTop',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n//删除智能体\r\nexport const deleteAgentApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgent/delete',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取banner列表\r\nexport const getBannerListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'merchant/api.index/bannerList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取我的收益\r\nexport const getMyEarningsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.AiAgentFinance/myEarnings',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n// 聊天点数商品列表\r\nexport const getChatGoodsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'square/api.chatGoods/index',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 购买聊天点数\r\nexport const buyChatGoodsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'square/api.chatGoods/buy',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 查询购买聊天点数结果\r\nexport const queryPayChatStautsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'square/api.chatGoods/buyQuery',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 数字人创作隐私协议\r\nexport const userVoicePrivacyApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userWork/voicePrivacy',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 获取公共形象列表\r\nexport const commonPersonListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/getPersonList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 创建数字人视频任务\r\nexport const createVideoTaskApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/createVideo',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 获取视频详情\r\nexport const getVideoDetailApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/getVideoDetail',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 我的数字人作品列表\r\nexport const worksListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/getMyWorks',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 批量删除数字人作品\r\nexport const deleteWorksApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/batchDeleteWorks',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 创建定制数字人形象\r\nexport const createPersonkApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/createPerson',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 我的数字人形象列表\r\nexport const getMyPersonListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/getMyPersonList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 删除我的数字人形象\r\nexport const deletePersonApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/deletePerson',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 视频上传\r\nexport const uploadVideoApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userinfo/uploadVideo',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n\r\n// 计算数字人视频创作所需算力\r\nexport const calculatePointsApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'useragent/api.ChanjingDigitalHuman/calculatePoints',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//保存秘钥\r\nexport const saveSecretKeyApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userinfo/saveSecretKey',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n//获取秘钥列表\r\nexport const getSecretKeyListApi = (data) => {\r\n\treturn request({\r\n\t\turl: 'user/api.userinfo/getSecretKeyList',\r\n\t\tmethod: 'POST',\r\n\t\tdata\r\n\t})\r\n}\r\n"], "names": ["request"], "mappings": ";;AAIY,MAAC,aAAa,CAAC,SAAS;AACnC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,8BAA8B,CAAC,SAAS;AACpD,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAoBY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,qBAAqB,CAAC,SAAS;AAC3C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,sBAAsB,CAAC,SAAS;AAC5C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,kBAAkB,CAAC,SAAS;AACxC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,wBAAwB,CAAC,SAAS;AAC9C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,uBAAuB,CAAC,SAAS;AAC7C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAUY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,uBAAuB,CAAC,SAAS;AAC7C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAUY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,0BAA0B,CAAC,SAAS;AAChD,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,yBAAyB,CAAC,SAAS;AAC/C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,yBAAyB,CAAC,SAAS;AAC/C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,wBAAwB,CAAC,SAAS;AAC9C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,uBAAuB,CAAC,SAAS;AAC7C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAIY,MAAC,uBAAuB,CAAC,SAAS;AAC7C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,sBAAsB,CAAC,SAAS;AAC5C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,yBAAyB,CAAC,SAAS;AAC/C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,sBAAsB,CAAC,SAAS;AAC5C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,4BAA4B,CAAC,SAAS;AAClD,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAYY,MAAC,yBAAyB,CAAC,SAAS;AAC/C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,kBAAkB,CAAC,SAAS;AACxC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,kBAAkB,CAAC,SAAS;AACxC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,wBAAwB,CAAC,SAAS;AAC9C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,sBAAsB,CAAC,SAAS;AAC5C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,sBAAsB,CAAC,SAAS;AAC5C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,qBAAqB,CAAC,SAAS;AAC3C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,oBAAoB,CAAC,SAAS;AAC1C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,eAAe,CAAC,SAAS;AACrC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,iBAAiB,CAAC,SAAS;AACvC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,qBAAqB,CAAC,SAAS;AAC3C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAGY,MAAC,kBAAkB,CAAC,SAAS;AACxC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAYY,MAAC,qBAAqB,CAAC,SAAS;AAC3C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,mBAAmB,CAAC,SAAS;AACzC,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;AAEY,MAAC,sBAAsB,CAAC,SAAS;AAC5C,SAAOA,wBAAQ;AAAA,IACd,KAAK;AAAA,IACL,QAAQ;AAAA,IACR;AAAA,EACF,CAAE;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}