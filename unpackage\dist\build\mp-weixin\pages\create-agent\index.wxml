<view class="create-agent-page data-v-019199ed"><scroll-view class="page-scroll data-v-019199ed" scroll-y><view class="avatar-section data-v-019199ed"><view class="avatar-container data-v-019199ed" bindtap="{{e}}"><image src="{{a}}" class="avatar data-v-019199ed" mode="aspectFill"/><view wx:if="{{b}}" class="avatar-add data-v-019199ed"><image src="{{c}}" class="add-icon data-v-019199ed" mode="aspectFit"/></view><view wx:if="{{d}}" class="avatar-loading data-v-019199ed"><view class="loading-spinner data-v-019199ed"></view></view></view><text bindtap="{{g}}" class="{{['avatar-label', 'data-v-019199ed', h && 'disabled']}}">{{f}}</text><view class="secret-bnt data-v-019199ed" bindtap="{{i}}">秘钥管理</view></view><view class="form-section data-v-019199ed"><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">选择分类:</text><view class="category-selector data-v-019199ed" bindtap="{{l}}"><text class="{{['category-text', 'data-v-019199ed', k && 'placeholder']}}">{{j}}</text></view></view><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">名称:</text><input class="input data-v-019199ed" placeholder="输入名称" placeholder-class="placeholder" maxlength="50" value="{{m}}" bindinput="{{n}}"/></view><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">智能体描述:</text><block wx:if="{{r0}}"><textarea class="textarea data-v-019199ed" placeholder="输入描述" placeholder-class="placeholder" maxlength="500" value="{{o}}" bindinput="{{p}}"/></block></view><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">选择智能体类型:</text><view class="radio-group data-v-019199ed"><view wx:for="{{q}}" wx:for-item="type" wx:key="d" class="radio-item data-v-019199ed" bindtap="{{type.e}}"><view class="{{['radio', 'data-v-019199ed', type.b && 'checked']}}"><view wx:if="{{type.a}}" class="radio-inner data-v-019199ed"></view></view><text class="radio-text data-v-019199ed">{{type.c}}</text></view></view></view><view wx:if="{{r}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">角色设定提示词:</text><block wx:if="{{r0}}"><textarea class="textarea large data-v-019199ed" placeholder="输入提示词" placeholder-class="placeholder" maxlength="2000" value="{{s}}" bindinput="{{t}}"/></block></view><view wx:if="{{v}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">密钥读取类型:</text><view class="radio-group data-v-019199ed"><view wx:for="{{w}}" wx:for-item="visibility" wx:key="d" class="radio-item data-v-019199ed" bindtap="{{visibility.e}}"><view class="{{['radio', 'data-v-019199ed', visibility.b && 'checked']}}"><view wx:if="{{visibility.a}}" class="radio-inner data-v-019199ed"></view></view><text class="radio-text data-v-019199ed">{{visibility.c}}</text></view></view></view><view wx:if="{{x}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">扣子智能体ID:</text><input class="input data-v-019199ed" placeholder="输入密码" placeholder-class="placeholder" value="{{y}}" bindinput="{{z}}"/></view><view wx:if="{{A}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">智能体秘钥:</text><input class="input data-v-019199ed" placeholder="输入秘钥" placeholder-class="placeholder" value="{{B}}" bindinput="{{C}}"/></view><view wx:if="{{D}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">dify自定义部署地址:</text><input class="input data-v-019199ed" placeholder="输入地址" placeholder-class="placeholder" value="{{E}}" bindinput="{{F}}"/></view><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">开场白:</text><input class="input data-v-019199ed" placeholder="输入开场白" placeholder-class="placeholder" value="{{G}}" bindinput="{{H}}"/></view><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">引导问题列表:</text><view class="question-list data-v-019199ed"><view wx:for="{{I}}" wx:for-item="question" wx:key="d" class="question-item data-v-019199ed"><input class="question-input data-v-019199ed" placeholder="{{question.a}}" placeholder-class="placeholder" value="{{question.b}}" bindinput="{{question.c}}"/></view></view></view><view class="form-item data-v-019199ed"><text class="label data-v-019199ed">选择智能体类型:</text><view class="radio-group data-v-019199ed"><view wx:for="{{J}}" wx:for-item="visibility" wx:key="d" class="radio-item data-v-019199ed" bindtap="{{visibility.e}}"><view class="{{['radio', 'data-v-019199ed', visibility.b && 'checked']}}"><view wx:if="{{visibility.a}}" class="radio-inner data-v-019199ed"></view></view><text class="radio-text data-v-019199ed">{{visibility.c}}</text></view></view></view><view wx:if="{{K}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">收费金额:</text><input class="input data-v-019199ed" placeholder="输入金额" placeholder-class="placeholder" type="digit" value="{{L}}" bindinput="{{M}}"/></view><view wx:if="{{N}}" class="form-item data-v-019199ed"><text class="label data-v-019199ed">试用聊天次数:</text><input class="input data-v-019199ed" placeholder="试用聊天次数" placeholder-class="placeholder" type="digit" value="{{O}}" bindinput="{{P}}"/></view><view class="form-note data-v-019199ed"><text class="note-text data-v-019199ed">注意：智能体不得违反相关法律法规，禁止涉及政治敏感话题，详情请查看《平台协议》</text></view></view><view class="create-section data-v-019199ed"><view bindtap="{{R}}" class="{{['create-btn', 'data-v-019199ed', S && 'disabled']}}"><text class="create-text data-v-019199ed">{{Q}}</text></view></view></scroll-view><view wx:if="{{T}}" class="category-modal data-v-019199ed" bindtap="{{ag}}"><view class="modal-content data-v-019199ed" catchtap="{{af}}"><view class="modal-header data-v-019199ed"><text class="modal-title data-v-019199ed">选择分类</text></view><view class="search-section data-v-019199ed"><view class="search-box data-v-019199ed"><image src="{{U}}" class="search-icon data-v-019199ed" mode="aspectFit"/><input class="search-input data-v-019199ed" placeholder="搜索分类" placeholder-class="placeholder" bindinput="{{V}}" value="{{W}}"/><view wx:if="{{X}}" class="clear-btn data-v-019199ed" bindtap="{{Z}}"><image src="{{Y}}" class="clear-icon data-v-019199ed" mode="aspectFit"/></view></view></view><scroll-view class="category-list data-v-019199ed" scroll-y><view wx:for="{{aa}}" wx:for-item="category" wx:key="d" class="category-item data-v-019199ed" bindtap="{{category.e}}"><view class="{{['radio', 'data-v-019199ed', category.b && 'checked']}}"><view wx:if="{{category.a}}" class="radio-inner data-v-019199ed"></view></view><text class="category-name data-v-019199ed">{{category.c}}</text></view><view wx:if="{{ab}}" class="empty-state data-v-019199ed"><text class="empty-text data-v-019199ed">暂无匹配的分类</text></view></scroll-view><view class="modal-footer data-v-019199ed"><view class="cancel-btn data-v-019199ed" bindtap="{{ac}}"><text class="cancel-text data-v-019199ed">取消</text></view><view bindtap="{{ad}}" class="{{['confirm-btn', 'data-v-019199ed', ae && 'disabled']}}"><text class="confirm-text data-v-019199ed">确认</text></view></view></view></view></view>