{"version": 3, "file": "uv-badge.js", "sources": ["uni_modules/uv-badge/components/uv-badge/uv-badge.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RToveW91bmdQcm9qZWN0L2FnZW50LW1pbmktdWkvdW5pX21vZHVsZXMvdXYtYmFkZ2UvY29tcG9uZW50cy91di1iYWRnZS91di1iYWRnZS52dWU"], "sourcesContent": ["<template>\r\n\t<text\r\n\t\tv-if=\"show && ((Number(value) === 0 ? showZero : true) || isDot)\"\r\n\t\t:class=\"[isDot ? 'uv-badge--dot' : 'uv-badge--not-dot', inverted && 'uv-badge--inverted', shape === 'horn' && 'uv-badge--horn', `uv-badge--${propsType}${inverted ? '--inverted' : ''}`]\"\r\n\t\t:style=\"[$uv.addStyle(customStyle), badgeStyle]\"\r\n\t\tclass=\"uv-badge\"\r\n\t>{{ isDot ? '' :showValue }}</text>\r\n</template>\r\n\r\n<script>\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\timport props from './props.js';\r\n\t/**\r\n\t * badge 徽标数\r\n\t * @description 该组件一般用于图标右上角显示未读的消息数量，提示用户点击，有圆点和圆包含文字两种形式。\r\n\t * @tutorial https://www.uvui.cn/components/badge.html\r\n\t * \r\n\t * @property {Boolean} \t\t\tisDot \t\t是否显示圆点 （默认 false ）\r\n\t * @property {String | Number} \tvalue \t\t显示的内容\r\n\t * @property {Boolean} \t\t\tshow \t\t是否显示 （默认 true ）\r\n\t * @property {String | Number} \tmax \t\t最大值，超过最大值会显示 '{max}+'  （默认999）\r\n\t * @property {String} \t\t\ttype \t\t主题类型，error|warning|success|primary （默认 'error' ）\r\n\t * @property {Boolean} \t\t\tshowZero\t当数值为 0 时，是否展示 Badge （默认 false ）\r\n\t * @property {String} \t\t\tbgColor \t背景颜色，优先级比type高，如设置，type参数会失效\r\n\t * @property {String} \t\t\tcolor \t\t字体颜色 （默认 '#ffffff' ）\r\n\t * @property {String} \t\t\tshape \t\t徽标形状，circle-四角均为圆角，horn-左下角为直角 （默认 'circle' ）\r\n\t * @property {String} \t\t\tnumberType\t设置数字的显示方式，overflow|ellipsis|limit  （默认 'overflow' ）\r\n\t * @property {Array}} \t\t\toffset\t\t设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，absolute为true时有效\r\n\t * @property {Boolean} \t\t\tinverted\t是否反转背景和字体颜色（默认 false ）\r\n\t * @property {Boolean} \t\t\tabsolute\t是否绝对定位（默认 false ）\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * @example <uv-badge :type=\"type\" :count=\"count\"></uv-badge>\r\n\t */\r\n\texport default {\r\n\t\tname: 'uv-badge',\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tcomputed: {\r\n\t\t\t// 是否将badge中心与父组件右上角重合\r\n\t\t\tboxStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 整个组件的样式\r\n\t\t\tbadgeStyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tif(this.color) {\r\n\t\t\t\t\tstyle.color = this.color\r\n\t\t\t\t}\r\n\t\t\t\tif (this.bgColor && !this.inverted) {\r\n\t\t\t\t\tstyle.backgroundColor = this.bgColor\r\n\t\t\t\t}\r\n\t\t\t\tif (this.absolute) {\r\n\t\t\t\t\tstyle.position = 'absolute'\r\n\t\t\t\t\t// 如果有设置offset参数\r\n\t\t\t\t\tif(this.offset.length) {\r\n\t\t\t\t\t\t// top和right分为为offset的第一个和第二个值，如果没有第二个值，则right等于top\r\n\t\t\t\t\t\tconst top = this.offset[0]\r\n\t\t\t\t\t\tconst right = this.offset[1] || top\r\n\t\t\t\t\t\tstyle.top = this.$uv.addUnit(top)\r\n\t\t\t\t\t\tstyle.right = this.$uv.addUnit(right)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\tshowValue() {\r\n\t\t\t\tswitch (this.numberType) {\r\n\t\t\t\t\tcase \"overflow\":\r\n\t\t\t\t\t\treturn Number(this.value) > Number(this.max) ? this.max + \"+\" : this.value\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"ellipsis\":\r\n\t\t\t\t\t\treturn Number(this.value) > Number(this.max) ? \"...\" : this.value\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"limit\":\r\n\t\t\t\t\t\treturn Number(this.value) > 999 ? Number(this.value) >= 9999 ?\r\n\t\t\t\t\t\t\tMath.floor(this.value / 1e4 * 100) / 100 + \"w\" : Math.floor(this.value /\r\n\t\t\t\t\t\t\t\t1e3 * 100) / 100 + \"k\" : this.value\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn Number(this.value)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpropsType(){\r\n\t\t\t\treturn this.type || 'error'\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/components.scss';\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/color.scss';\r\n\t$uv-badge-primary: $uv-primary !default;\r\n\t$uv-badge-error: $uv-error !default;\r\n\t$uv-badge-success: $uv-success !default;\r\n\t$uv-badge-info: $uv-info !default;\r\n\t$uv-badge-warning: $uv-warning !default;\r\n\t$uv-badge-dot-radius: 100px !default;\r\n\t$uv-badge-dot-size: 8px !default;\r\n\t$uv-badge-dot-right: 4px !default;\r\n\t$uv-badge-dot-top: 0 !default;\r\n\t$uv-badge-text-font-size: 11px !default;\r\n\t$uv-badge-text-right: 10px !default;\r\n\t$uv-badge-text-padding: 2px 5px !default;\r\n\t$uv-badge-text-align: center !default;\r\n\t$uv-badge-text-color: #FFFFFF !default;\r\n\r\n\t.uv-badge {\r\n\t\tborder-top-right-radius: $uv-badge-dot-radius;\r\n\t\tborder-top-left-radius: $uv-badge-dot-radius;\r\n\t\tborder-bottom-left-radius: $uv-badge-dot-radius;\r\n\t\tborder-bottom-right-radius: $uv-badge-dot-radius;\r\n\t\t@include flex;\r\n\t\tline-height: $uv-badge-text-font-size;\r\n\t\ttext-align: $uv-badge-text-align;\r\n\t\tfont-size: $uv-badge-text-font-size;\r\n\t\tcolor: $uv-badge-text-color;\r\n\r\n\t\t&--dot {\r\n\t\t\theight: $uv-badge-dot-size;\r\n\t\t\twidth: $uv-badge-dot-size;\r\n\t\t}\r\n\t\t\r\n\t\t&--inverted {\r\n\t\t\tfont-size: 13px;\r\n\t\t}\r\n\t\t\r\n\t\t&--not-dot {\r\n\t\t\tpadding: $uv-badge-text-padding;\r\n\t\t}\r\n\r\n\t\t&--horn {\r\n\t\t\tborder-bottom-left-radius: 0;\r\n\t\t}\r\n\r\n\t\t&--primary {\r\n\t\t\tbackground-color: $uv-badge-primary;\r\n\t\t}\r\n\t\t\r\n\t\t&--primary--inverted {\r\n\t\t\tcolor: $uv-badge-primary;\r\n\t\t}\r\n\r\n\t\t&--error {\r\n\t\t\tbackground-color: $uv-badge-error;\r\n\t\t}\r\n\t\t\r\n\t\t&--error--inverted {\r\n\t\t\tcolor: $uv-badge-error;\r\n\t\t}\r\n\r\n\t\t&--success {\r\n\t\t\tbackground-color: $uv-badge-success;\r\n\t\t}\r\n\t\t\r\n\t\t&--success--inverted {\r\n\t\t\tcolor: $uv-badge-success;\r\n\t\t}\r\n\r\n\t\t&--info {\r\n\t\t\tbackground-color: $uv-badge-info;\r\n\t\t}\r\n\t\t\r\n\t\t&--info--inverted {\r\n\t\t\tcolor: $uv-badge-info;\r\n\t\t}\r\n\r\n\t\t&--warning {\r\n\t\t\tbackground-color: $uv-badge-warning;\r\n\t\t}\r\n\t\t\r\n\t\t&--warning--inverted {\r\n\t\t\tcolor: $uv-badge-warning;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import Component from 'E:/youngProject/agent-mini-ui/uni_modules/uv-badge/components/uv-badge/uv-badge.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props"], "mappings": ";;;;;AAkCC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,yDAAK;AAAA,EAC9B,UAAU;AAAA;AAAA,IAET,WAAW;AACV,UAAI,QAAQ,CAAA;AACZ,aAAO;AAAA,IACP;AAAA;AAAA,IAED,aAAa;AACZ,YAAM,QAAQ,CAAC;AACf,UAAG,KAAK,OAAO;AACd,cAAM,QAAQ,KAAK;AAAA,MACpB;AACA,UAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AACnC,cAAM,kBAAkB,KAAK;AAAA,MAC9B;AACA,UAAI,KAAK,UAAU;AAClB,cAAM,WAAW;AAEjB,YAAG,KAAK,OAAO,QAAQ;AAEtB,gBAAM,MAAM,KAAK,OAAO,CAAC;AACzB,gBAAM,QAAQ,KAAK,OAAO,CAAC,KAAK;AAChC,gBAAM,MAAM,KAAK,IAAI,QAAQ,GAAG;AAChC,gBAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK;AAAA,QACrC;AAAA,MACD;AACA,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,cAAQ,KAAK,YAAU;AAAA,QACtB,KAAK;AACJ,iBAAO,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,GAAG,IAAI,KAAK,MAAM,MAAM,KAAK;AAAA,QAEtE,KAAK;AACJ,iBAAO,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,GAAG,IAAI,QAAQ,KAAK;AAAA,QAE7D,KAAK;AACJ,iBAAO,OAAO,KAAK,KAAK,IAAI,MAAM,OAAO,KAAK,KAAK,KAAK,OACvD,KAAK,MAAM,KAAK,QAAQ,MAAM,GAAG,IAAI,MAAM,MAAM,KAAK,MAAM,KAAK,QAChE,MAAM,GAAG,IAAI,MAAM,MAAM,KAAK;AAAA,QAEjC;AACC,iBAAO,OAAO,KAAK,KAAK;AAAA,MAC1B;AAAA,IACA;AAAA,IACD,YAAW;AACV,aAAO,KAAK,QAAQ;AAAA,IACrB;AAAA,EACD;AACD;;;;;;;;;;;;;;;ACrFD,GAAG,gBAAgB,SAAS;"}