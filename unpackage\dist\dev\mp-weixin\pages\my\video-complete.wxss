/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.video-complete-page.data-v-f5ae4b12 {
  background: #F5F5F5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
}
.video-complete-page .video-container.data-v-f5ae4b12 {
  height: calc(100vh - 180rpx);
  background: #000000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.video-complete-page .video-container .video-player.data-v-f5ae4b12 {
  width: 100%;
  height: 100%;
  background: #000000;
}
.video-complete-page .bottom-actions.data-v-f5ae4b12 {
  background: #FFFFFF;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 180rpx;
}
.video-complete-page .bottom-actions .action-btn.data-v-f5ae4b12 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 330rpx;
  height: 90rpx;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 50rpx;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
}
.video-complete-page .bottom-actions .action-btn.data-v-f5ae4b12:active {
  opacity: 0.8;
}
.video-complete-page .bottom-actions .action-btn.data-v-f5ae4b12::after {
  border: none;
}
.video-complete-page .bottom-actions .action-btn .action-icon.data-v-f5ae4b12 {
  width: 40rpx;
  height: 40rpx;
  margin-right: 14rpx;
}
.video-complete-page .bottom-actions .action-btn .action-text.data-v-f5ae4b12 {
  font-weight: 500;
}
.video-complete-page .bottom-actions .action-btn.save-btn.data-v-f5ae4b12 {
  background: #ECECEC;
  border: 2rpx solid #E9ECEF;
}
.video-complete-page .bottom-actions .action-btn.save-btn .action-text.data-v-f5ae4b12 {
  color: #333333;
}
.video-complete-page .bottom-actions .action-btn.share-btn.data-v-f5ae4b12 {
  background: #2A64F6;
}
.video-complete-page .bottom-actions .action-btn.share-btn .action-text.data-v-f5ae4b12 {
  color: #FFFFFF;
}