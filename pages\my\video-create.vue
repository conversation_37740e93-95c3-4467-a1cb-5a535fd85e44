<template>
	<view class="video-create-page">
		<!-- 视频预览区域 -->
		<view class="video-preview-container">
			<view class="video-preview-card">
				<!-- 数字人形象 -->
				<view class="digital-person-image">
                    <view class="person-box">
					    <image class="person-avatar" :src="currentAvatar" mode="aspectFill" />
                        <!-- 提示 -->
						<view class="video-tips" v-if="showSubtitle">请在脚本区编辑您的正文</view>
                        <!-- 更换按钮 -->
                        <view class="change-btn" @tap="openVideoModal">
                            <image class="change-icon" src="/static/my/template_change.png" mode="aspectFit" />
							<text>更换</text>
                        </view>
                    </view>
					<!-- 视频时长和字幕控制 -->
					<view class="video-controls">
						<view class="video-duration"></view>
						<view class="subtitle-checkbox" @tap="onSubtitleChange">
							<image
								class="subtitle-icon"
								:src="showSubtitle ? '/static/my/select-icon1.png' : '/static/my/select-icon2.png'"
								mode="aspectFit"
							/>
							<text class="subtitle-text">字幕</text>
						</view>
					</view>
					
				</view>

				<!-- 文本内容区域 -->
				<view class="text-content-area">
					<textarea
						class="content-textarea"
						v-model="textContent"
						placeholder="请输入文本内容..."
						:maxlength="4000"
						:show-confirm-bar="false"
					/>

					<!-- 底部操作按钮 -->
					<!-- <view class="bottom-actions">
						<view class="action-btn" @tap="handleRandom">
							<image class="action-icon" src="/static/my/template_random.png" mode="aspectFit" />
							<text class="action-text">随机</text>
						</view>
						<view class="action-btn" @tap="handleVoice">
							<image class="action-icon" src="/static/my/template_voice.png" mode="aspectFit" />
							<text class="action-text">{{ currentVoiceText }}</text>
						</view>
					</view> -->
				</view>
			</view>

			<!-- 立即生成按钮 -->
			<view class="generate-btn" @tap="handleGenerateVideo">
				<text class="generate-text">立即生成</text>
			</view>
		</view>

		<!-- 视频模板选择弹窗 -->
		<view v-if="showVideoModal" class="video-overlay" @tap="closeVideoModal">
			<view class="video-modal" @tap.stop>
				<view class="video-header">
					<view class="primary-tabs">
						<view
							v-for="(primaryTab, index) in primaryTabs"
							:key="index"
							class="primary-tab"
							:class="{ active: activePrimaryTab === index }"
							@tap="switchPrimaryTab(index)"
						>
							<text class="primary-tab-text">{{ primaryTab }}</text>
						</view>
					</view>
					<view class="confirm-icon-wrapper" @tap="confirmTemplateSelection">
						<image class="confirm-icon" src="/static/my/template_submit.png" mode="aspectFit"></image>
					</view>
				</view>

				<!-- 模板内容 -->
				<scroll-view
					scroll-y="true"
					class="template-scroll"
					@scrolltolower="loadMoreTemplates"
					:lower-threshold="50"
				>
					<!-- 空状态 -->
					<view class="empty-state" v-if="templateList.length === 0 && currentPage === 1 && !isLoadingMore">
						<view class="empty-icon-placeholder">
							<text class="empty-icon-text">📋</text>
						</view>
						<text class="empty-text">暂无数据</text>
					</view>

					<!-- 模板列表 -->
					<view class="video-grid" v-else>
						<view
							v-for="(template, index) in templateList"
							:key="index"
							class="video-template"
							:class="{ selected: selectedTemplate && selectedTemplate.id === template.id }"
							@tap="selectTemplate(template)"
						>
							<image class="template-image" :src="getTemplateImage(template)" mode="aspectFill" />
						</view>
					</view>

					<!-- 加载更多提示 -->
					<view class="load-more" v-if="templateList.length > 0">
						<view v-if="isLoadingMore" class="loading-text">加载中...</view>
						<view v-else-if="hasMoreData" class="loading-text">上拉加载更多</view>
						<view v-else class="loading-text">没有更多了</view>
					</view>
				</scroll-view>

				<view class="close-icon-wrapper" @tap="closeVideoModal">
					<image class="close-icon" src="/static/my/popup-close.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>

		<!-- 配音选择弹窗 -->
		<view v-if="showVoiceModal" class="voice-overlay" @tap="closeVoiceModal">
			<view class="voice-modal" @tap.stop>
				<view class="voice-header">
					<text class="voice-title">配音</text>
					<view class="confirm-icon-wrapper" @tap="confirmVoiceSelection">
						<image class="confirm-icon" src="/static/my/template_submit.png" mode="aspectFit"></image>
					</view>
				</view>

				<view class="voice-content">
					<view class="voice-tip">
						<text class="tip-text">恢复形象默认声音</text>
					</view>

					<view class="voice-tabs">
					<view
						v-for="(tab, index) in voiceTabs"
						:key="index"
						class="voice-tab"
						:class="{ active: activeVoiceTab === index }"
						@tap="switchVoiceTab(index)"
					>
						<text class="tab-text">{{ tab }}</text>
					</view>
				</view>

				<!-- 配音内容 -->
				<view class="voice-grid">
					<view
						v-for="(voice, index) in currentVoices"
						:key="index"
						class="voice-item"
						:class="{ selected: selectedVoice && selectedVoice.id === voice.id }"
						@tap="selectVoice(voice)"
					>
						<text class="voice-name">{{ voice.name }}</text>
					</view>
				</view>
			</view>
		</view>
		</view>

		<!-- 隐私协议弹窗 -->
		<view v-if="showPrivacyModal" class="privacy-overlay" @tap="closePrivacyModal">
			<view class="privacy-modal" @tap.stop>
				<view class="privacy-header">
					<text class="privacy-title">隐私协议</text>
				</view>

				<scroll-view scroll-y="true" class="privacy-content" show-scrollbar="false">
					<view class="privacy-rich-content">
						<rich-text
							:nodes="privacyContent"
							class="privacy-rich-text"
							:user-select="true"
						></rich-text>
					</view>
				</scroll-view>

				<view class="privacy-footer">
					<view
						class="privacy-btn"
						:class="{ disabled: !canConfirm }"
						@tap="confirmPrivacy"
					>
						<text class="privacy-btn-text">
							{{ canConfirm ? '我同意' : `我同意(${countdown}s)` }}
						</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { userVoicePrivacyApi, commonPersonListApi, getMyPersonListApi, createVideoTaskApi, getUserInfoApi, calculatePointsApi } from '@/api/index.js'
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()

// 视频模板弹窗相关状态
const showVideoModal = ref(false)
const activePrimaryTab = ref(0)
const primaryTabs = ref(['模板', '定制数字人'])
const selectedTemplate = ref(null)

// 模板列表数据
const templateList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const isLoadingMore = ref(false)
const hasMoreData = ref(true)

// 配音弹窗相关状态
const showVoiceModal = ref(false)
const activeVoiceTab = ref(0)
const voiceTabs = ref(['我的', '全部', '男性', '女性'])
const selectedVoice = ref(null)
const currentVoiceText = ref('情感女生') // 当前选中的配音文案

// 当前显示的头像
const currentAvatar = ref('')

// 字幕显示控制
const showSubtitle = ref(true)

// 隐私协议弹窗相关状态
const showPrivacyModal = ref(false)
const privacyContent = ref('')
const canConfirm = ref(false)
const countdown = ref(3)

// 文本内容
const textContent = ref('')

// 加载模板列表
const loadTemplateList = async (page = 1, isLoadMore = false, autoSelectFirst = false) => {
	if (isLoadingMore.value && isLoadMore) return

	try {
		if (isLoadMore) {
			isLoadingMore.value = true
		}

		let res
		if (activePrimaryTab.value === 0) {
			// 模板 - 调用commonPersonListApi
			res = await commonPersonListApi({
				merchantGuid: userStore.merchantGuid,
				page: page,
				pageSize: pageSize.value
			})
		} else {
			// 定制数字人 - 调用getMyPersonListApi
			res = await getMyPersonListApi({
				merchantGuid: userStore.merchantGuid,
				page: page,
				pageSize: pageSize.value
			})
		}

		const newList = res.data.list || []
			console.log(newList)

		if (page === 1) {
			// 第一页，直接替换
			templateList.value = newList
			console.log(autoSelectFirst)
			console.log(templateList.value)

			// 如果需要自动选中第一个模板
			if (autoSelectFirst && newList.length > 0) {
				console.log(newList[0])
				selectedTemplate.value = newList[0]
				currentAvatar.value = getTemplateImage(newList[0])
				console.log(currentAvatar.value)
				console.log(selectedTemplate.value)
			}
		} else {
			// 后续页，追加到列表
			templateList.value = [...templateList.value, ...newList]
		}

		// 判断是否还有更多数据
		const pageInfo = res.data.pageInfo || {}
		hasMoreData.value = page < (pageInfo.totalPage || 1)

	} catch (error) {
		console.error('获取模板列表失败:', error)
		uni.showToast({
			title: '加载失败',
			icon: 'none'
		})
	} finally {
		if (isLoadMore) {
			isLoadingMore.value = false
		}
	}
}

// 加载更多模板
const loadMoreTemplates = () => {
	if (!hasMoreData.value || isLoadingMore.value) return

	currentPage.value++
	loadTemplateList(currentPage.value, true)
}

// 获取模板图片
const getTemplateImage = (template) => {
	if (activePrimaryTab.value === 0) {
		// 定制数字人数据结构：figures[0].cover
		return template.figures?.[0]?.cover
	} else {
		// 模板数据结构：picUrl
		return template.picUrl
	}
}

// 获取图片信息的通用函数
const getImageInfo = async (imageSrc) => {
	try {
		const result = await uni.getImageInfo({
			src: imageSrc
		})
		console.log('图片信息',result)
		return {
			width: result.width,
			height: result.height,
			path: result.path,
			orientation: result.orientation,
			type: result.type
		}
	} catch (error) {
		console.error('获取图片信息失败:', error)
		return null
	}
}

// 配音数据 - 固定内容
const allVoices = ref([
	{ id: 1, name: '说话大爷', category: 'my', type: 'male' },
	{ id: 2, name: '做饭小哥', category: 'all', type: 'male' },
	{ id: 3, name: '爽朗阿姨', category: 'all', type: 'female' },
	{ id: 4, name: '高冷学妹', category: 'all', type: 'female' },
	{ id: 5, name: '激情朗诵小哥', category: 'all', type: 'male' },
	{ id: 6, name: '演讲女孩', category: 'all', type: 'female' },
	{ id: 7, name: '做饭小哥', category: 'all', type: 'male' },
	{ id: 8, name: '爽朗阿姨', category: 'all', type: 'female' },
	{ id: 9, name: '高冷学妹', category: 'all', type: 'female' },
	{ id: 10, name: '科普男声', category: 'all', type: 'male' },
	{ id: 11, name: '演讲女孩', category: 'all', type: 'female' },
	{ id: 12, name: '做饭小哥', category: 'all', type: 'male' },
	{ id: 13, name: '爽朗阿姨', category: 'all', type: 'female' },
	{ id: 14, name: '高冷学妹', category: 'all', type: 'female' },
	{ id: 15, name: '科普男声', category: 'all', type: 'male' },
	{ id: 16, name: '演讲女孩', category: 'all', type: 'female' },
	{ id: 17, name: '做饭小哥', category: 'all', type: 'male' },
	{ id: 18, name: '爽朗阿姨', category: 'all', type: 'female' },
	{ id: 19, name: '高冷学妹', category: 'all', type: 'female' },
	{ id: 20, name: '科普男声', category: 'all', type: 'male' }
])



// 当前显示的配音
const currentVoices = computed(() => {
	if (activeVoiceTab.value === 0) {
		return allVoices.value.filter(v => v.category === 'my')
	} else if (activeVoiceTab.value === 1) {
		return allVoices.value
	} else if (activeVoiceTab.value === 2) {
		return allVoices.value.filter(v => v.type === 'male')
	} else {
		return allVoices.value.filter(v => v.type === 'female')
	}
})

// 视频模板弹窗相关方法
const openVideoModal = () => {
	showVideoModal.value = true
	selectedTemplate.value = null // 重置选中状态
	activePrimaryTab.value = 0 // 重置到模板页面
	// 重置分页状态并加载第一页数据
	currentPage.value = 1
	hasMoreData.value = true
	loadTemplateList(1)
}

const closeVideoModal = () => {
	showVideoModal.value = false
	selectedTemplate.value = null // 清除选中状态
}

// 仅关闭弹窗，不清除选中状态
const closeVideoModalOnly = () => {
	showVideoModal.value = false
}

const switchPrimaryTab = (index) => {
	activePrimaryTab.value = index
	selectedTemplate.value = null // 切换一级菜单时清除选中状态
	// 重置分页状态并重新加载数据
	currentPage.value = 1
	hasMoreData.value = true
	loadTemplateList(1)
}

const selectTemplate = (template) => {
	selectedTemplate.value = template
	console.log('选择模板:', template)
}

const confirmTemplateSelection = () => {
	console.log(selectedTemplate.value)
	if (selectedTemplate.value) {
		console.log('确认选择模板:', selectedTemplate.value)
		// 更新当前显示的头像
		currentAvatar.value = getTemplateImage(selectedTemplate.value)

		uni.showToast({
			title: `切换成功`,
			icon: 'success',
			duration: 2000
		})
		// 仅关闭弹窗，保留选中状态
		closeVideoModalOnly()
	} else {
		uni.showToast({
			title: '请先选择一个模板',
			icon: 'none',
			duration: 2000
		})
	}
}

// 处理字幕变化
const onSubtitleChange = () => {
	showSubtitle.value = !showSubtitle.value
}

// 处理随机按钮点击
const handleRandom = () => {
	// 这里可以添加随机生成文本的逻辑
	uni.showToast({
		title: '随机生成中...',
		icon: 'loading',
		duration: 1500
	})
}

// 处理情感女生按钮点击
const handleVoice = () => {
	showVoiceModal.value = true
	selectedVoice.value = null // 重置选中状态
	activeVoiceTab.value = 0 // 重置到我的页面
}

// 配音弹窗相关方法
const openVoiceModal = () => {
	showVoiceModal.value = true
	selectedVoice.value = null // 重置选中状态
	activeVoiceTab.value = 0 // 重置到我的页面
}

const closeVoiceModal = () => {
	showVoiceModal.value = false
	selectedVoice.value = null // 清除选中状态
}

const switchVoiceTab = (index) => {
	activeVoiceTab.value = index
}

const selectVoice = (voice) => {
	selectedVoice.value = voice
}

const confirmVoiceSelection = () => {
	if (selectedVoice.value) {
		// 更新当前显示的配音文案
		currentVoiceText.value = selectedVoice.value.name
		uni.showToast({
			title: `已选择：${selectedVoice.value.name}`,
			icon: 'success',
			duration: 2000
		})
		closeVoiceModal()
	} else {
		uni.showToast({
			title: '请先选择一个配音',
			icon: 'none',
			duration: 2000
		})
	}
}

// 获取隐私协议内容
const getPrivacyContent = async () => {
	try {
		const res = await userVoicePrivacyApi()
		privacyContent.value = res.data
	} catch (error) {
		console.error('获取隐私协议失败:', error)
		privacyContent.value = '<p style="color: #ff6b6b;text-align: center;">获取隐私协议内容失败，请稍后重试。</p>'
	}
}

// 开始倒计时
const startCountdown = () => {
	canConfirm.value = false
	countdown.value = 3

	const timer = setInterval(() => {
		countdown.value--
		if (countdown.value <= 0) {
			clearInterval(timer)
			canConfirm.value = true
		}
	}, 1000)
}

// 处理生成视频
const handleGenerateVideo = async () => {
	console.log(selectedTemplate.value)
	if (!selectedTemplate.value) {
		uni.showToast({
			title: '请先选择模板',
			icon: 'none'
		})
		return
	}
	if (!textContent.value.trim()) {
		uni.showToast({
			title: '请输入文本内容',
			icon: 'none'
		})
		return
	}

	try {
		// 显示检查中提示
		uni.showLoading({
			title: '检查算力中...',
			mask: true
		})

		// 准备计算算力的参数（与创建视频任务相同的参数）
		const calculateParams = {
			merchantGuid: userStore.merchantGuid,
			text: textContent.value,
			audioType: 'tts', // 默认音频类型
		}

		if (activePrimaryTab.value === 0) {
			// 模板数据结构
			calculateParams.personId = selectedTemplate.value.id
			calculateParams.audioManId = selectedTemplate.value.audioManId
			calculateParams.figureType = selectedTemplate.value.figures[0]?.type
		} else {
			// 定制数字人数据结构
			calculateParams.personId = selectedTemplate.value.chanjingPersonId
			calculateParams.audioManId = selectedTemplate.value.audioManId
			calculateParams.figureType = ''
		}

		// 并行调用两个接口
		const [userInfoRes, calculateRes] = await Promise.all([
			getUserInfoApi(),
			calculatePointsApi(calculateParams)
		])

		uni.hideLoading()

		// 检查用户算力是否足够
		const userPoints = userInfoRes.data.chat_count || 0
		const requiredPoints = calculateRes.data.requiredPoints || 0

		if (userPoints < requiredPoints) {
			uni.showModal({
				title: '算力不足',
				content: `当前算力：${userPoints}，所需算力：${requiredPoints}，请先充值算力`,
				showCancel: false,
				confirmText: '去充值',
				success: (res) => {
					if (res.confirm) {}
				}
			})
			return
		}

		// 算力足够，显示隐私协议弹窗
		showPrivacyModal.value = true
		await getPrivacyContent()
		startCountdown()

	} catch (error) {
		uni.hideLoading()
		console.error('检查算力失败:', error)
		uni.showToast({
			title: '检查失败，请重试',
			icon: 'none'
		})
	}
}

// 确认隐私协议
const confirmPrivacy = async () => {
	if (!canConfirm.value) return

	showPrivacyModal.value = false

	try {
		// 显示创建中提示
		uni.showToast({
			title: '创建中...',
			icon: 'loading',
			duration: 0, // 持续显示直到手动隐藏
			mask: true
		})

		// 准备创建视频任务的参数
		const createParams = {
			merchantGuid: userStore.merchantGuid,
			text: textContent.value,
			audioType: 'tts', // 默认音频类型
			subtitleShow: showSubtitle.value
		}
		let previewUrl = '';
		if (activePrimaryTab.value === 0) {
			// 模板数据结构
			createParams.personId = selectedTemplate.value.id
			createParams.audioManId = selectedTemplate.value.audioManId
			createParams.figureType = selectedTemplate.value.figures[0]?.type
			previewUrl = selectedTemplate.value.figures[0]?.cover
		} else {
			// 定制数字人数据结构
			createParams.personId = selectedTemplate.value.chanjingPersonId
			createParams.audioManId = selectedTemplate.value.audioManId
			createParams.figureType = ''
			previewUrl = selectedTemplate.value.picUrl
		}
		// 获取图片的宽高
		console.log(previewUrl)

		// 获取封面图的宽高
		const imageInfo = await getImageInfo(previewUrl)
		if (imageInfo) {
			createParams.personWidth = imageInfo.width
			createParams.personHeight = imageInfo.height
		}

		
		// 调用创建视频任务API
		const res = await createVideoTaskApi(createParams)
		// 隐藏loading提示
		uni.hideToast()

		if (res.code === 0) {
			uni.showToast({
				title: '视频任务创建成功',
				icon: 'success'
			})
			// 跳转到视频生成进度页面
			uni.navigateTo({
				url: `/pages/my/video-progress?orderNo=${res.data.orderNo}&previewUrl=${previewUrl}`
			})
		} else {
			uni.showToast({
				title: res.msg || '创建失败',
				icon: 'none'
			})
		}
	} catch (error) {
		// 隐藏loading提示
		uni.hideToast()
		console.error('创建视频任务失败:', error)
		uni.showToast({
			title: error.meg || '创建失败，请重试',
			icon: 'none'
		})
	}
}

// 关闭隐私协议弹窗
const closePrivacyModal = () => {
	showPrivacyModal.value = false
}

// 页面初始化
onMounted(() => {
	// 页面加载时默认加载模板列表并选中第一个
	currentPage.value = 1
	hasMoreData.value = true
	activePrimaryTab.value = 0 // 默认选中模板菜单
	loadTemplateList(1, false, true) // 第三个参数为true表示自动选中第一个
})
</script>

<style lang="scss" scoped>
.video-create-page {
	background: #141115;
	min-height: 100vh;
	padding: 32rpx;
    box-sizing: border-box;

	.video-preview-container {
		.video-preview-card {
			background: #141215;
			border-radius: 24rpx;
			padding: 0;
			margin-bottom: 32rpx;
			position: relative;
			overflow: hidden;

			.digital-person-image {
				width: 100%;

                .person-box{
                    width: 390rpx;
                    height: 690rpx;
                    margin: auto;
                    position: relative;
                    
                    .person-avatar {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
					.video-tips{
						position: absolute;
                        bottom: 80rpx;
                        right: 0;
						left: 0;
						text-align: center;
						color: white;
						font-size: 30rpx;
						font-weight: 500;
						/* 文字描边效果 */
						text-shadow:
							-1px -1px 0 #000,
							1px -1px 0 #000,
							-1px 1px 0 #000,
							1px 1px 0 #000,
							-2px 0 0 #000,
							2px 0 0 #000,
							0 -2px 0 #000,
							0 2px 0 #000;
						/* 或者使用webkit的描边属性（备选方案） */
						// -webkit-text-stroke: 1px #000;
					}
                    .change-btn {
                        position: absolute;
                        bottom: 14rpx;
                        right: 14rpx;
                        padding: 7rpx 14rpx;
                        background: rgba(0, 0, 0, 0.9);
                        color: white;
                        font-size: 24rpx;
                        font-weight: 400;
                        border-radius: 12rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .change-icon {
                            width: 30rpx;
                            height: 30rpx;
                        }
                    }
                }
				

				.video-controls {
					display: flex;
					align-items: center;
                    justify-content: space-between;
                    margin: 30rpx auto;

					.video-duration {
						color: #ffffff;
						font-size: 26rpx;
					}

					.subtitle-checkbox {
						display: flex;
						align-items: center;

						.subtitle-icon {
							width: 30rpx;
							height: 30rpx;
							margin-right: 8rpx;
						}

						.subtitle-text {
							color: #ffffff;
							font-size: 26rpx;
						}
					}
				}
			}

			.text-content-area {
				background: #212121;
                border-radius: 25rpx;
				padding: 32rpx;
				margin: 0;
				display: flex;
				flex-direction: column;

				.content-textarea {
					font-size: 28rpx;
                    font-weight: 400;
					line-height: 1.6;
					color: #fff;
                    width: 100%;
					height: 296rpx;
					border: none;
					outline: none;
					resize: none;
					background: transparent;
				}

				.bottom-actions {
					display: flex;
                    align-items: center;
					justify-content: space-between;
                    margin-top: 20rpx;

					.action-btn {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
						padding: 8rpx 14rpx;
						background: #333333;
						border-radius: 12rpx;
                        color: white;

						&:active {
							background: #e9ecef;
						}

						.action-icon {
							width: 30rpx;
							height: 30rpx;
                            margin-right: 5rpx;
						}

						.action-text {
							font-size: 24rpx;
							color: white;
							font-weight: 400;
						}
					}
				}
			}
		}

		.generate-btn {
			background: #3478f6;
			border-radius: 48rpx;
			padding: 32rpx;
			text-align: center;

			.generate-text {
				font-size: 32rpx;
				color: #ffffff;
				font-weight: 600;
			}
		}
	}

	// 视频模板弹窗样式
	.video-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: flex-end;
		justify-content: center;
		z-index: 9999;
	}

	.video-modal {
		background: #232325;
		border-radius: 32rpx 32rpx 0 0;
		padding: 48rpx 30rpx 32rpx;
		width: 100%;
		max-height: 80vh;
		position: relative;
		color: #ffffff;

		.video-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;
			padding: 0 16rpx;
			position: relative;

			.primary-tabs {
				display: flex;
				align-items: center;

				.primary-tab {
					margin-right: 32rpx;
                    padding-bottom: 18rpx;
					&.active {
						.primary-tab-text {
							color: #ffffff;
							font-weight: 500;
							border-bottom: 3rpx solid #ffffff;
						}
					}

					.primary-tab-text {
						font-size: 30rpx;
                        font-weight: 400;
						color: #999999;
						transition: color 0.3s ease;
						padding-bottom: 18rpx;
					}
				}
			}

			.confirm-icon-wrapper {
				position: absolute;
				right: 16rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.confirm-icon {
					width: 60rpx;
					height: 60rpx;
				}
			}
		}



		.template-scroll {
			height: 800rpx;
			padding: 0 16rpx;
		}

		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 600rpx;
			padding: 40rpx;

			.empty-icon-placeholder {
				width: 120rpx;
				height: 120rpx;
				margin-bottom: 24rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 60rpx;

				.empty-icon-text {
					font-size: 60rpx;
					opacity: 0.6;
				}
			}

			.empty-text {
				font-size: 32rpx;
				color: #666666;
				margin-bottom: 12rpx;
				font-weight: 500;
			}
		}

		.load-more {
			padding: 32rpx 0;
			text-align: center;

			.loading-text {
				font-size: 28rpx;
				color: #999999;
			}
		}

		.video-grid {
			display: flex;
			flex-wrap: wrap;
			align-content: flex-start;

			.video-template {
				width: 220rpx;
				height: 350rpx;
				border-radius: 16rpx;
				overflow: hidden;
				border: 4rpx solid transparent;
				transition: border-color 0.3s ease;
				margin-bottom: 16rpx;
                box-sizing: border-box;

				&.selected {
					border-color: #3478f6;
				}

				.template-image {
					width: 100%;
					height: 100%;
					object-fit: cover;
				}
			}
			.video-template:not(:nth-child(3n)){
				margin-right: 12rpx;
			}
		}



		.close-icon-wrapper {
			position: absolute;
			bottom: -80rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 45rpx;
			height: 45rpx;
			border-radius: 50%;

			.close-icon {
				width: 45rpx;
				height: 45rpx;
			}
		}
	}

    // 配音弹窗样式
    .voice-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: flex-end;
        justify-content: center;
        z-index: 9999;
    }

    .voice-modal {
        background: #232325;
        border-radius: 32rpx 32rpx 0 0;
        padding: 0;
        width: 100%;
        max-height: 80vh;
        position: relative;
        color: #ffffff;

        .voice-header {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 32rpx 30rpx 28rpx;
            position: relative;
            border-bottom: 2rpx solid #2A2A2A;

            .voice-title {
                font-size: 30rpx;
                font-weight: 500;
                color: #ffffff;
            }

            .confirm-icon-wrapper {
                position: absolute;
                right: 30rpx;
                bottom: 8rpx;
                width: 60rpx;
                height: 60rpx;

                .confirm-icon {
                    width: 100%;
                    height: 100%;
                    display: block;
                }
            }
        }

        .voice-content {
            padding: 32rpx 30rpx;

            .voice-tip {
                margin-bottom: 32rpx;

                .tip-text {
                    font-size: 24rpx;
                    color: #999999;
                    font-weight: 400;
                }
            }
        }

        .voice-tabs {
            display: flex;
            margin-bottom: 32rpx;

            .voice-tab {
                background: #333333;
                border-radius: 32rpx;
                padding: 6rpx 18rpx;
                margin-right: 16rpx;

                &.active {
                    background: #585858;
                    border: 2rpx solid #8D8D8D;

                    .tab-text {
                        font-weight: 500;
                        font-size: 30rpx;
                    }
                }

                .tab-text {
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #ffffff;
                }
            }
        }

        .voice-grid {
            display: flex;
            align-content: flex-start;
            flex-wrap: wrap;
            height: 600rpx;
            overflow-y: auto;

            .voice-item {
                background: #333333;
                border-radius: 16rpx;
                padding: 20rpx 6rpx;
                text-align: center;
                border: 4rpx solid transparent;
                transition: all 0.3s ease;
                box-sizing: border-box;
                width: 120rpx;
                height: 120rpx;
                overflow: hidden;
                display: flex;
                align-items: center;
                margin-bottom: 20rpx;

                &.selected {
                    border-color: #3478f6;

                    .voice-name {
                        color: #AFC6FF;
                    }
                }

                .voice-name {
                    font-size: 24rpx;
                    color: #ffffff;
                    font-weight: 400;
                    word-break: break-all;
                }
            }
            .voice-item:not(:nth-child(5n)){
                margin-right: 22rpx;
            }
        }
    }

	// 隐私协议弹窗样式
	.privacy-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.7);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.privacy-modal {
		background: #ffffff;
		border-radius: 24rpx;
		width: 600rpx;
		max-width: 90vw;
		max-height: 80vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.privacy-header {
			padding: 40rpx 40rpx 20rpx;
			text-align: center;

			.privacy-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #333333;
			}
		}

		.privacy-content {
			flex: 1;
			padding: 20rpx 40rpx 30rpx;
			max-height: 600rpx;
			box-sizing: border-box;
			overflow: hidden;

			.privacy-rich-content {
				.privacy-rich-text {
					font-size: 28rpx;
					line-height: 1.6;
					color: #666666;
					word-break: break-word;
				}
			}
		}

		.privacy-footer {
			padding: 30rpx 40rpx 40rpx;

			.privacy-btn {
				background: #3478f6;
				border-radius: 48rpx;
				padding: 24rpx;
				text-align: center;
				transition: all 0.3s ease;

				&.disabled {
					background: #cccccc;
					cursor: not-allowed;
				}

				.privacy-btn-text {
					font-size: 30rpx;
					color: #ffffff;
					font-weight: 500;
				}
			}
		}
	}
}
</style>