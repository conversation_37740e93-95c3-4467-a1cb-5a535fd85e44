"use strict";const e=require("../../common/vendor.js"),t=require("../../common/assets.js"),a=require("../../api/index.js"),i=require("../../stores/user.js"),n=require("../../api/common.js"),s=require("../../utils/utils.js"),o=require("../../config/config.js");if(!Array){(e.resolveComponent("ua-markdown")+e.resolveComponent("z-paging")+e.resolveComponent("uni-popup"))()}Math||((()=>"../../components/ua-markdown/ua-markdown.js")+(()=>"../../uni_modules/z-paging/components/z-paging/z-paging.js")+(()=>"../../uni_modules/uni-popup/components/uni-popup/uni-popup.js")+l)();const l=()=>"../../components/subscribe-popup/subscribe-popup.js",r={__name:"index",setup(l){const r=e.ref(null),c=e.ref(null),d=i.useUserStore(),u=e.ref(""),g=e.ref(""),v=e.ref(!0),m=e.ref(""),p=e.ref([]),h=e.ref(!1),w=e.ref(!1),f=requirePlugin("WechatSI").getRecordRecognitionManager(),x=e.ref(!1),y=e.reactive({agentName:"",agentAvatar:"",agentDesc:"",price:0}),I=e.ref([]),T=e.ref(!1),G=e.ref(0),M=async()=>{e.index.showModal({title:"确认重置",content:"确定要重置对话吗？",success:async t=>{if(t.confirm){let t=await a.deleteAllMessagesApi({merchantGuid:d.merchantGuid,sessionGuid:g.value});0===t.code?(e.index.showToast({title:"已重置",icon:"success"}),clearInterval(N.value),C.msgId="",h.value=!1,v.value=!1,await r.value.reload(),b(y.agentDesc),F.value=!1):e.index.showToast({title:t.msg,icon:"none"})}}})},$=()=>{e.index.showToast({title:"近期上线，敬请关注",icon:"none"})};let A=e.ref(!0);const _=async(t,i)=>{let n={merchantGuid:d.merchantGuid,sessionGuid:g.value,startId:0,page:t,isAll:0};try{let t=await a.getMessageHistoryApi(n);A.value=!1,e.index.setNavigationBarTitle({title:t.data.sessionInfo.agentInfo.agentName});let i=[];t.data.list.length>0&&(C.msgId=t.data.list[0].msgId,t.data.list.forEach((e=>{if("assistant"===e.chatRole){let t=e.contentType;"img"===e.contentType&&(t="image"),"url"===e.contentType&&(t="link"),null===e.imageList&&(e.imageList=[]),i.push({role:"assistant",content:e.chatContent,isSuccessData:!0,isNewMsg:!1,type:t,guid:e.guid,aiReplyTitle:"",msgId:e.lastMsgId,isCollected:e.isCollected,imageList:e.imageList})}else"user"===e.chatRole&&i.push({role:"user",content:e.chatContent,msgId:e.msgId})}))),i.length>0&&"assistant"===i[i.length-1].role?i[i.length-1].isFirst=!0:v.value&&(console.log("这里什么情况"),i.push({role:"assistant",content:y.agentDesc,isSuccessData:!1,isLoading:!1,type:"text",aiReplyTitle:"",isFirst:!0,msgId:"",guid:"first_none_guid",imageList:[]}),v.value=!1),await r.value.complete(i)}catch(s){A.value=!1,r.value.complete(!1)}},L=s.throttle((e=>{D.value=e,B()}),1e3),b=e=>{let t={role:"assistant",content:e,isSuccessData:!1,isLoading:!1,type:"text",aiReplyTitle:"",isFirst:!0,isCollected:!1,msgId:"",guid:"first_none_guid",imageList:[]};r.value.addChatRecordData(t)},D=e.ref(""),C=e.reactive({isNewGlobalMsg:!1,nowAiMsgIndex:0,msgId:"",nowChatLunciGuid:""});let R=e.ref(!0);const k=()=>{R.value=!1},S=()=>{R.value=!0},N=e.ref(""),z=e.ref(30),P=async()=>{let t="",i=!1,n=!0;N.value&&(clearInterval(N.value),N.value="");let s=`${o.base.baseUrl}/useragent/api.AiAgentChat/sendOpen?msgId=${C.msgId}&chatLunciGuid=${g.value}&merchantGuid=${d.merchantGuid}&conversation_id=${m.value}`;const l=e.wx$1.request({url:s,method:"GET",enableChunked:!0,timeout:6e5});l.onChunkReceived((s=>{try{if(n){n=!1;let s="",o=0;N.value=setInterval((async()=>{t=t.replace(/\\n/g,"\n");let n=s;if(s.length<t.length)s+=t[o],o+=1;else if(i){h.value=!1,clearInterval(N.value),N.value="",q.content=s,q.lastMsgId=C.msgId,q.contentType="richText",q.sessionGuid=g.value,p.value[C.nowAiMsgIndex].isLoading=!1;try{let t=await a.saveMsgApi(q);if(0!==t.code)return C.isNewGlobalMsg=!1,void(p.value[C.nowAiMsgIndex].content="e-2服务繁忙，请稍候重试");C.msgId=t.data.msgId,C.isNewGlobalMsg=!1,p.value[C.nowAiMsgIndex].guid=t.data.messageGuid,p.value[C.nowAiMsgIndex].isSuccessData=!0,e.nextTick$1((()=>{r.value.scrollToBottom()}))}catch(l){if(900001===l.code)return w.value=!0,void(h.value=!1)}}s!==n&&(p.value[C.nowAiMsgIndex].content=s)}),z.value)}let o=s.data,c=new Uint8Array(o),d=e.index.arrayBufferToBase64(c);d=(t=>{const a=e.index.base64ToArrayBuffer(t);return(new e.encodingExports.TextDecoder).decode(a)})(d);let u=(e=>{let t=[];return e.trim().split("\n").forEach((e=>{if(e.length>0&&e){const[a,i]=e.split(": ");i&&i.trim()&&t.push({[a]:i})}})),t})(d);console.log(u,"textArr"),u.map((e=>{if(e.hasOwnProperty("data")&&e.data){if("[DONE]"==e.data)return m.value=u[u.length-2].id,i=!0,void l.abort();""===t?t=e.data.replace(/^\n+/,""):t+=e.data}}))}catch(o){console.log(o,"onChunkReceivederror"),C.isNewGlobalMsg=!1,p.value[C.nowAiMsgIndex].content="e-3服务繁忙，请稍候重试",p.value[C.nowAiMsgIndex].isSuccessData=!0,clearInterval(N.value)}}))};let j=e.reactive({merchantGuid:d.merchantGuid,sessionGuid:"",role:"user",content:"",lastMsgId:"",contentType:"text",sceneValue:"",chatLunciGuid:""}),q=e.reactive({merchantGuid:d.merchantGuid,role:"assistant",sessionGuid:"",content:"",lastMsgId:"",contentType:"",sceneValue:"",chatLunciGuid:"",imgs:[]});const E=()=>{B()};let F=e.ref(!1);const B=async()=>{if(0!==D.value.trim().length)if(h.value)e.wx$1.showToast({title:"回答中...",icon:"none",duration:1e3});else{h.value=!0,N.value&&(clearInterval(N.value),N.value=""),(e=>{let t={role:"user",content:e,msgId:"",isLoading:!0,guid:`user_${Date.now()}_${Math.random()}`};r.value.addChatRecordData(t)})(D.value),((e,t="text",a=!1)=>{let i={content:"",title:"",imageList:[]},n={role:"assistant",content:e,isSuccessData:!1,isLoading:!0,type:t,aiReplyTitle:i.title,isFirst:a,isCollected:!1,msgId:"",guid:`ai_${Date.now()}_${Math.random()}`,imageList:i.imageList};r.value.addChatRecordData(n)})(" "),C.nowAiMsgIndex=0,j.content=D.value,j.lastMsgId=C.msgId,j.sessionGuid=g.value;try{let e=await a.saveMsgApi(j);if(F.value=!0,console.log(e,"----------msgReqRes"),0!==e.code)return p.value[C.nowAiMsgIndex].content="e-2服务繁忙，请稍候重试",void(h.value=!1);C.msgId=e.data.msgId,p.value[C.nowAiMsgIndex+1].msgId=e.data.msgId,D.value="",P()}catch(t){if(console.log(t,"---------error"),h.value=!1,p.value[C.nowAiMsgIndex].isLoading=!1,900001===t.code)return p.value[C.nowAiMsgIndex].content="试用次数已用完，请订阅激活",void(w.value=!0);p.value[C.nowAiMsgIndex].content=`${t.msg}-${t.code}`}}else e.wx$1.showToast({title:"请输入问题 ...",icon:"none",duration:1e3})},O=e=>{c.value.close()},U=(t,a,i)=>{let n=[];a?n=t:n[0]=t,e.index.previewImage({current:i,urls:n,longPressActions:{itemList:["发送给朋友","保存图片","收藏"],success:function(e){},fail:function(e){console.log(e.errMsg)}}})},V=e.reactive({recorderMode:1,isRecorderMode:!1,voiceText:"按住说话",voiceTitle:"松手结束录音",sendLock:!0,isCloseSend:!1,duration:6e4,startPoint:null,isAnalyzeDisabled:!1,recording:!1}),Y=t=>{x.value?V.isAnalyzeDisabled?e.wx$1.showToast({title:"正在识别语音中...",icon:"none",duration:1e3}):C.isNewGlobalMsg?e.wx$1.showToast({title:"正在回答中...",icon:"none",duration:1e3}):(V.startPoint=t.touches[0],V.recorderMode=2,V.voiceText="说话中...",V.recording=!0,f.start({duration:6e4,lang:"zh_CN"}),V.sendLock=!1):e.wx$1.showToast({title:"当前版本不支持,或您未打开麦克风使用权限",icon:"none",duration:1e3})},H=()=>{V.isCloseSend?K():V.isAnalyzeDisabled||C.isNewGlobalMsg||(K(),V.recording&&(f.stop(),V.isAnalyzeDisabled=!0,V.sendLock||e.index.showLoading({title:"识别中",mask:!0})))},J=e=>{if(!e||!e.touches||0===e.touches.length||!V.startPoint)return;let t=e.touches[e.touches.length-1].clientY-V.startPoint.clientY;Math.abs(t)>30?(V.voiceTitle="松开手指,取消发送",V.voiceText="松开手指,取消发送",V.sendLock=!0,V.isCloseSend=!0):(V.voiceTitle="松手结束录音",V.voiceText="松手结束录音",V.sendLock=!1,V.isCloseSend=!1)},K=()=>{V.recorderMode=1,V.voiceText="按住说话",V.voiceTitle="松手结束录音",V.isCloseSend=!1},Q=()=>{T.value=!0},W=()=>{T.value=!1},X=async()=>{let t=await a.createPurchaseOrderApi({merchantGuid:d.merchantGuid,agentGuid:u.value,payEnv:"xcx"});n.miniPay(t.data.payInfo).then((async e=>{Z(t.data.orderNo,G)}),(t=>{e.index.showToast({title:t.msg})}))},Z=async(t,i)=>{i++;try{(await a.queryPurchaseOrderApi({orderNo:t})).data.isPaid?(e.index.showToast({title:"支付成功"}),w.value=!1,T.value=!1):i>12?e.index.showToast({title:"支付失败"}):Z(t,i)}catch(n){e.index.showToast({title:n.msg?n.msg:"支付失败"})}};return e.onLoad((async t=>{e.wx$1.getSetting({success(t){t.authSetting["scope.record"]?x.value=!0:e.wx$1.authorize({scope:"scope.record",success(){x.value=!0},fail(){x.value=!1}})}}),f.onRecognize=e=>{V.sendLock||(D.value=e.result,E())},f.onStop=t=>{if(console.log("onStoponStop",t),V.isAnalyzeDisabled=!1,V.recording=!1,V.sendLock)return;e.index.hideLoading();let a=t.result;""!=a&&(D.value=a,E())},f.onError=t=>{e.index.hideLoading(),V.isAnalyzeDisabled=!1,V.recording=!1,-30003===t.retcode?e.wx$1.showToast({title:"未检测到语音",icon:"none",duration:1e3}):e.wx$1.showToast({title:"未检测到语音~",icon:"none",duration:1e3})},t.sessionGuid&&(u.value=t.sessionGuid,await(async()=>{try{const e=await a.subscribeAgentApi({merchantGuid:d.merchantGuid,agentGuid:u.value});g.value=e.data.sessionGuid}catch(t){e.index.showToast({title:t.msg||"订阅失败",icon:"none"})}})(),await(async()=>{let e=await a.agentDetailApi({merchantGuid:d.merchantGuid,agentGuid:u.value});y.agentName=e.data.agentName,y.agentAvatar=e.data.agentAvatar,I.value=e.data.commonQuestions,y.agentDesc=e.data.agentDesc,y.price=e.data.priceYuan})()),d.userToken&&e.nextTick$1((()=>{r.value&&r.value.reload()}))})),(i,n)=>e.e({a:e.f(p.value,((i,n,s)=>e.e({a:"assistant"===i.role},"assistant"===i.role?e.e({b:i.aiReplyTitle&&i.aiReplyTitle.length>0},i.aiReplyTitle&&i.aiReplyTitle.length>0?{c:e.t(i.aiReplyTitle)}:{},{d:"richText"===i.type},"richText"===i.type?e.e({e:i.content,f:i.imageList&&i.imageList.length>0},i.imageList&&i.imageList.length>0?{g:e.f(i.imageList,((t,a,n)=>({a:e.o((e=>U(i.imageList,!0,a)),a),b:t,c:a}))),h:i.imageList.length<3?1:""}:{}):{},{i:"text"===i.type},"text"===i.type?e.e({j:"b7e31647-1-"+s+",b7e31647-0",k:e.p({source:i.content}),l:i.isLoading},(i.isLoading,{}),{m:i.isFirst&&!e.unref(F)},i.isFirst&&!e.unref(F)?{n:e.f(I.value,((t,a,i)=>({a:e.t(t),b:e.o((a=>e.unref(L)(t)),a),c:a})))}:{}):{},{o:"image"===i.type},"image"===i.type?{p:e.o((e=>U(i.content,!1,0)),i.guid||`temp_${n}_${i.role}_${Date.now()}`),q:i.content}:{},{r:"link"===i.type},"link"===i.type?{s:e.t(i.content),t:e.o((t=>{return a=i.content,void e.index.navigateTo({url:"/pages/webview/webview",success:e=>{e.eventChannel.emit("urlEvent",decodeURIComponent(a))},fail(e){console.log(e)}});var a}),i.guid||`temp_${n}_${i.role}_${Date.now()}`)}:{},{v:"video"===i.type},"video"===i.type?{w:i.content}:{},{x:"mini"===i.type},"mini"===i.type?{y:e.o((t=>{return a=i.content,void e.index.navigateToMiniProgram({appId:a,success(e){console.log(e,"success")},fail(e){console.log(e,"fail")}});var a}),i.guid||`temp_${n}_${i.role}_${Date.now()}`)}:{},{z:!i.isFirst},i.isFirst?{}:e.e({A:i.guid},i.guid?{B:e.o((t=>(async t=>{let i=t.guid,n="收藏成功";try{t.isCollected?(await a.cancelCollectMessageApi({merchantGuid:d.merchantGuid,messageGuid:i}),n="取消收藏成功",t.isCollected=!1):(await a.collectMessageApi({merchantGuid:d.merchantGuid,messageGuid:i}),t.isCollected=!0),e.index.showToast({title:n,icon:"success"})}catch(s){e.index.showToast({title:s.msg,icon:"none"})}})(i)),i.guid||`temp_${n}_${i.role}_${Date.now()}`),C:i.isCollected?e.unref(t.scOnIcon):e.unref(t.scIcon)}:{},{D:e.o((t=>{return a=i.content,void e.index.setClipboardData({data:a,success(){e.index.showToast({title:"复制成功",icon:"none"})}});var a}),i.guid||`temp_${n}_${i.role}_${Date.now()}`),E:t._imports_0$8,F:n===p.value.findIndex((e=>"assistant"===e.role))},n===p.value.findIndex((e=>"assistant"===e.role))?{G:e.o($,i.guid||`temp_${n}_${i.role}_${Date.now()}`)}:{}),{H:0===i.aiReplyTitle.length?1:""}):{},{I:"user"===i.role},"user"===i.role?{J:e.t(i.content)}:{},{K:i.guid||`temp_${n}_${i.role}_${Date.now()}`}))),b:y.agentAvatar,c:e.unref(R)&&!w.value},e.unref(R)&&!w.value?{d:e.o(M)}:{},{e:w.value},w.value?{f:e.o(Q)}:e.e({g:!V.isRecorderMode},V.isRecorderMode?{}:e.e({h:e.unref(A),i:e.o(k),j:e.o(S),k:e.o(B),l:D.value,m:e.o((e=>D.value=e.detail.value)),n:e.unref(R)},e.unref(R)?{o:t._imports_1$5,p:e.o(Y),q:e.o(H),r:e.o(J)}:{s:t._imports_2$4,t:e.o(B)}),{v:2==V.recorderMode},2==V.recorderMode?{w:e.t(V.voiceTitle),x:V.isCloseSend?1:""}:{}),{y:e.sr(r,"b7e31647-0",{k:"paging"}),z:e.o(_),A:e.o((e=>p.value=e)),B:e.p({"auto-hide-keyboard-when-chat":!1,"hide-empty-view":!0,"use-chat-record-mode":!0,auto:!1,"auto-clean-list-when-reload":!1,"show-chat-loading-when-reload":!0,modelValue:p.value}),C:e.o(O),D:e.sr(c,"b7e31647-2",{k:"authPopup"}),E:e.o(W),F:e.o(X),G:e.p({show:T.value,agentInfo:y})})}},c=e._export_sfc(r,[["__scopeId","data-v-b7e31647"]]);wx.createPage(c);
