"use strict";
const request_request = require("../request/request.js");
const mpLoginApi = (data) => {
  return request_request.request({
    url: "user/api.user/xcxSilenceLogin",
    method: "POST",
    data
  });
};
const updateUserDefaultProfileApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentSquare/updateUserDefaultProfile",
    method: "POST",
    data
  });
};
const getHomeDataApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentPublic/homeData",
    method: "POST",
    data
  });
};
const getCategoryListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentPublic/categoryList",
    method: "POST",
    data
  });
};
const getMySessionListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/mySessionList",
    method: "POST",
    data
  });
};
const getAgentListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentSquare/agentList",
    method: "POST",
    data
  });
};
const subscribeAgentApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentSquare/subscribeAgent",
    method: "POST",
    data
  });
};
const updateSessionTitleApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/updateSessionTitle",
    method: "POST",
    data
  });
};
const deleteSessionApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/deleteSession",
    method: "POST",
    data
  });
};
const updateUserInfoApi = (data) => {
  return request_request.request({
    url: "user/api.userinfo/update",
    method: "POST",
    data
  });
};
const getUserInfoApi = (data) => {
  return request_request.request({
    url: "user/api.userinfo/index",
    method: "POST",
    data
  });
};
const getMyAgentListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/myList",
    method: "POST",
    data
  });
};
const createAgentApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/create",
    method: "POST",
    data
  });
};
const generateAvatarApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/generateAvatar",
    method: "POST",
    data
  });
};
const getMyDetailApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/myDetail",
    method: "POST",
    data
  });
};
const updateMyAgentApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/update",
    method: "POST",
    data
  });
};
const getMessageHistoryApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/messageHistory",
    method: "POST",
    data
  });
};
const platformRulesApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentPublic/platformRules",
    method: "POST",
    data
  });
};
const agentDetailApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/agentDetail",
    method: "POST",
    data
  });
};
const deleteAllMessagesApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/deleteAllMessages",
    method: "POST",
    data
  });
};
const collectMessageApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/collectMessage",
    method: "POST",
    data
  });
};
const cancelCollectMessageApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/uncollectMessage",
    method: "POST",
    data
  });
};
const getMyCollectionListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/myCollectionList",
    method: "POST",
    data
  });
};
const createPurchaseOrderApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentSquare/createPurchaseOrder",
    method: "POST",
    data
  });
};
const queryPurchaseOrderApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentSquare/queryPurchaseOrder",
    method: "POST",
    data
  });
};
const createVipOrderApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentMembership/purchasePackage",
    method: "POST",
    data
  });
};
const getVipPackageListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentMembership/packageList",
    method: "POST",
    data
  });
};
const getUserVipInfoApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentMembership/myMembership",
    method: "POST",
    data
  });
};
const queryVipOrderApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentMembership/queryPayment",
    method: "POST",
    data
  });
};
const getInvitationCodeApi = (data) => {
  return request_request.request({
    url: "user/api.userinfo/getInvitationCode",
    method: "POST",
    data
  });
};
const bindInvitationApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/bindInvitation",
    method: "POST",
    data
  });
};
const generateMiniCodeApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/generateMiniCode",
    method: "POST",
    data
  });
};
const getSubscriptionListApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentCreatorSubscription/creatorList",
    method: "POST",
    data
  });
};
const subscribeCreatorApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentCreatorSubscription/purchaseCreatorSubscription",
    method: "POST",
    data
  });
};
const querySubscriptionOrderApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentCreatorSubscription/queryPayment",
    method: "POST",
    data
  });
};
const getSubscriptionRuleApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentMembership/subscriptionRule",
    method: "POST",
    data
  });
};
const showBannerUrlsApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentPublic/showBannerUrls",
    method: "POST",
    data
  });
};
const setSessionTopApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentChat/setSessionTop",
    method: "POST",
    data
  });
};
const deleteAgentApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgent/delete",
    method: "POST",
    data
  });
};
const getBannerListApi = (data) => {
  return request_request.request({
    url: "merchant/api.index/bannerList",
    method: "POST",
    data
  });
};
const getMyEarningsApi = (data) => {
  return request_request.request({
    url: "useragent/api.AiAgentFinance/myEarnings",
    method: "POST",
    data
  });
};
const getChatGoodsApi = (data) => {
  return request_request.request({
    url: "square/api.chatGoods/index",
    method: "POST",
    data
  });
};
const buyChatGoodsApi = (data) => {
  return request_request.request({
    url: "square/api.chatGoods/buy",
    method: "POST",
    data
  });
};
const queryPayChatStautsApi = (data) => {
  return request_request.request({
    url: "square/api.chatGoods/buyQuery",
    method: "POST",
    data
  });
};
const userVoicePrivacyApi = (data) => {
  return request_request.request({
    url: "user/api.userWork/voicePrivacy",
    method: "POST",
    data
  });
};
const commonPersonListApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/getPersonList",
    method: "POST",
    data
  });
};
const createVideoTaskApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/createVideo",
    method: "POST",
    data
  });
};
const getVideoDetailApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/getVideoDetail",
    method: "POST",
    data
  });
};
const worksListApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/getMyWorks",
    method: "POST",
    data
  });
};
const deleteWorksApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/batchDeleteWorks",
    method: "POST",
    data
  });
};
const createPersonkApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/createPerson",
    method: "POST",
    data
  });
};
const getMyPersonListApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/getMyPersonList",
    method: "POST",
    data
  });
};
const deletePersonApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/deletePerson",
    method: "POST",
    data
  });
};
const calculatePointsApi = (data) => {
  return request_request.request({
    url: "useragent/api.ChanjingDigitalHuman/calculatePoints",
    method: "POST",
    data
  });
};
const saveSecretKeyApi = (data) => {
  return request_request.request({
    url: "user/api.userinfo/saveSecretKey",
    method: "POST",
    data
  });
};
const getSecretKeyListApi = (data) => {
  return request_request.request({
    url: "user/api.userinfo/getSecretKeyList",
    method: "POST",
    data
  });
};
exports.agentDetailApi = agentDetailApi;
exports.bindInvitationApi = bindInvitationApi;
exports.buyChatGoodsApi = buyChatGoodsApi;
exports.calculatePointsApi = calculatePointsApi;
exports.cancelCollectMessageApi = cancelCollectMessageApi;
exports.collectMessageApi = collectMessageApi;
exports.commonPersonListApi = commonPersonListApi;
exports.createAgentApi = createAgentApi;
exports.createPersonkApi = createPersonkApi;
exports.createPurchaseOrderApi = createPurchaseOrderApi;
exports.createVideoTaskApi = createVideoTaskApi;
exports.createVipOrderApi = createVipOrderApi;
exports.deleteAgentApi = deleteAgentApi;
exports.deleteAllMessagesApi = deleteAllMessagesApi;
exports.deletePersonApi = deletePersonApi;
exports.deleteSessionApi = deleteSessionApi;
exports.deleteWorksApi = deleteWorksApi;
exports.generateAvatarApi = generateAvatarApi;
exports.generateMiniCodeApi = generateMiniCodeApi;
exports.getAgentListApi = getAgentListApi;
exports.getBannerListApi = getBannerListApi;
exports.getCategoryListApi = getCategoryListApi;
exports.getChatGoodsApi = getChatGoodsApi;
exports.getHomeDataApi = getHomeDataApi;
exports.getInvitationCodeApi = getInvitationCodeApi;
exports.getMessageHistoryApi = getMessageHistoryApi;
exports.getMyAgentListApi = getMyAgentListApi;
exports.getMyCollectionListApi = getMyCollectionListApi;
exports.getMyDetailApi = getMyDetailApi;
exports.getMyEarningsApi = getMyEarningsApi;
exports.getMyPersonListApi = getMyPersonListApi;
exports.getMySessionListApi = getMySessionListApi;
exports.getSecretKeyListApi = getSecretKeyListApi;
exports.getSubscriptionListApi = getSubscriptionListApi;
exports.getSubscriptionRuleApi = getSubscriptionRuleApi;
exports.getUserInfoApi = getUserInfoApi;
exports.getUserVipInfoApi = getUserVipInfoApi;
exports.getVideoDetailApi = getVideoDetailApi;
exports.getVipPackageListApi = getVipPackageListApi;
exports.mpLoginApi = mpLoginApi;
exports.platformRulesApi = platformRulesApi;
exports.queryPayChatStautsApi = queryPayChatStautsApi;
exports.queryPurchaseOrderApi = queryPurchaseOrderApi;
exports.querySubscriptionOrderApi = querySubscriptionOrderApi;
exports.queryVipOrderApi = queryVipOrderApi;
exports.saveSecretKeyApi = saveSecretKeyApi;
exports.setSessionTopApi = setSessionTopApi;
exports.showBannerUrlsApi = showBannerUrlsApi;
exports.subscribeAgentApi = subscribeAgentApi;
exports.subscribeCreatorApi = subscribeCreatorApi;
exports.updateMyAgentApi = updateMyAgentApi;
exports.updateSessionTitleApi = updateSessionTitleApi;
exports.updateUserDefaultProfileApi = updateUserDefaultProfileApi;
exports.updateUserInfoApi = updateUserInfoApi;
exports.userVoicePrivacyApi = userVoicePrivacyApi;
exports.worksListApi = worksListApi;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/index.js.map
