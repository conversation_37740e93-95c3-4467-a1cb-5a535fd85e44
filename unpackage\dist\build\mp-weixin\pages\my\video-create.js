"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),l=require("../../api/index.js"),o=require("../../stores/user.js"),t={__name:"video-create",setup(t){const n=o.useUserStore(),u=e.ref(!1),i=e.ref(0),r=e.ref(["模板","定制数字人"]),v=e.ref(null),s=e.ref([]),c=e.ref(1),d=e.ref(10),m=e.ref(!1),g=e.ref(!0),y=e.ref(!1),p=e.ref(0),f=e.ref(["我的","全部","男性","女性"]),h=e.ref(null),w=e.ref("情感女生"),x=e.ref(""),T=e.ref(!0),I=e.ref(!1),_=e.ref(""),M=e.ref(!1),P=e.ref(3),$=e.ref(""),b=async(a=1,o=!1,t=!1)=>{if(!m.value||!o)try{let e;o&&(m.value=!0),e=0===i.value?await l.commonPersonListApi({merchantGuid:n.merchantGuid,page:a,pageSize:d.value}):await l.getMyPersonListApi({merchantGuid:n.merchantGuid,page:a,pageSize:d.value});const u=e.data.list||[];console.log(u),1===a?(s.value=u,console.log(t),console.log(s.value),t&&u.length>0&&(console.log(u[0]),v.value=u[0],x.value=j(u[0]),console.log(x.value),console.log(v.value))):s.value=[...s.value,...u];const r=e.data.pageInfo||{};g.value=a<(r.totalPage||1)}catch(u){console.error("获取模板列表失败:",u),e.index.showToast({title:"加载失败",icon:"none"})}finally{o&&(m.value=!1)}},G=()=>{g.value&&!m.value&&(c.value++,b(c.value,!0))},j=e=>{var a,l;return 0===i.value?null==(l=null==(a=e.figures)?void 0:a[0])?void 0:l.cover:e.picUrl},A=e.ref([{id:1,name:"说话大爷",category:"my",type:"male"},{id:2,name:"做饭小哥",category:"all",type:"male"},{id:3,name:"爽朗阿姨",category:"all",type:"female"},{id:4,name:"高冷学妹",category:"all",type:"female"},{id:5,name:"激情朗诵小哥",category:"all",type:"male"},{id:6,name:"演讲女孩",category:"all",type:"female"},{id:7,name:"做饭小哥",category:"all",type:"male"},{id:8,name:"爽朗阿姨",category:"all",type:"female"},{id:9,name:"高冷学妹",category:"all",type:"female"},{id:10,name:"科普男声",category:"all",type:"male"},{id:11,name:"演讲女孩",category:"all",type:"female"},{id:12,name:"做饭小哥",category:"all",type:"male"},{id:13,name:"爽朗阿姨",category:"all",type:"female"},{id:14,name:"高冷学妹",category:"all",type:"female"},{id:15,name:"科普男声",category:"all",type:"male"},{id:16,name:"演讲女孩",category:"all",type:"female"},{id:17,name:"做饭小哥",category:"all",type:"male"},{id:18,name:"爽朗阿姨",category:"all",type:"female"},{id:19,name:"高冷学妹",category:"all",type:"female"},{id:20,name:"科普男声",category:"all",type:"male"}]),q=e.computed((()=>0===p.value?A.value.filter((e=>"my"===e.category)):1===p.value?A.value:2===p.value?A.value.filter((e=>"male"===e.type)):A.value.filter((e=>"female"===e.type)))),L=()=>{u.value=!0,v.value=null,i.value=0,c.value=1,g.value=!0,b(1)},U=()=>{u.value=!1,v.value=null},k=()=>{console.log(v.value),v.value?(console.log("确认选择模板:",v.value),x.value=j(v.value),e.index.showToast({title:"切换成功",icon:"success",duration:2e3}),u.value=!1):e.index.showToast({title:"请先选择一个模板",icon:"none",duration:2e3})},S=()=>{T.value=!T.value},z=()=>{y.value=!1,h.value=null},C=()=>{h.value?(w.value=h.value.name,e.index.showToast({title:`已选择：${h.value.name}`,icon:"success",duration:2e3}),z()):e.index.showToast({title:"请先选择一个配音",icon:"none",duration:2e3})},H=async()=>{var a;if(console.log(v.value),v.value)if($.value.trim())try{e.index.showLoading({title:"检查算力中...",mask:!0});const o={merchantGuid:n.merchantGuid,text:$.value,audioType:"tts"};0===i.value?(o.personId=v.value.id,o.audioManId=v.value.audioManId,o.figureType=null==(a=v.value.figures[0])?void 0:a.type):(o.personId=v.value.chanjingPersonId,o.audioManId=v.value.audioManId,o.figureType="");const[t,u]=await Promise.all([l.getUserInfoApi(),l.calculatePointsApi(o)]);e.index.hideLoading();const r=t.data.chat_count||0,s=u.data.requiredPoints||0;if(r<s)return void e.index.showModal({title:"算力不足",content:`当前算力：${r}，所需算力：${s}，请先充值算力`,showCancel:!1,confirmText:"去充值",success:e=>{e.confirm}});I.value=!0,await(async()=>{try{const e=await l.userVoicePrivacyApi();_.value=e.data}catch(e){console.error("获取隐私协议失败:",e),_.value='<p style="color: #ff6b6b;text-align: center;">获取隐私协议内容失败，请稍后重试。</p>'}})(),(()=>{M.value=!1,P.value=3;const e=setInterval((()=>{P.value--,P.value<=0&&(clearInterval(e),M.value=!0)}),1e3)})()}catch(o){e.index.hideLoading(),console.error("检查算力失败:",o),e.index.showToast({title:"检查失败，请重试",icon:"none"})}else e.index.showToast({title:"请输入文本内容",icon:"none"});else e.index.showToast({title:"请先选择模板",icon:"none"})},N=async()=>{var a,o;if(M.value){I.value=!1;try{e.index.showToast({title:"创建中...",icon:"loading",duration:0,mask:!0});const t={merchantGuid:n.merchantGuid,text:$.value,audioType:"tts",subtitleShow:T.value};let u="";0===i.value?(t.personId=v.value.id,t.audioManId=v.value.audioManId,t.figureType=null==(a=v.value.figures[0])?void 0:a.type,u=null==(o=v.value.figures[0])?void 0:o.cover):(t.personId=v.value.chanjingPersonId,t.audioManId=v.value.audioManId,t.figureType="",u=v.value.picUrl),console.log(u);const r=await(async a=>{try{const l=await e.index.getImageInfo({src:a});return console.log("图片信息",l),{width:l.width,height:l.height,path:l.path,orientation:l.orientation,type:l.type}}catch(l){return console.error("获取图片信息失败:",l),null}})(u);r&&(t.personWidth=r.width,t.personHeight=r.height);const s=await l.createVideoTaskApi(t);e.index.hideToast(),0===s.code?(e.index.showToast({title:"视频任务创建成功",icon:"success"}),e.index.navigateTo({url:`/pages/my/video-progress?orderNo=${s.data.orderNo}&previewUrl=${u}`})):e.index.showToast({title:s.msg||"创建失败",icon:"none"})}catch(t){e.index.hideToast(),console.error("创建视频任务失败:",t),e.index.showToast({title:t.meg||"创建失败，请重试",icon:"none"})}}},V=()=>{I.value=!1};return e.onMounted((()=>{c.value=1,g.value=!0,i.value=0,b(1,!1,!0)})),(l,o)=>e.e({a:x.value,b:T.value},(T.value,{}),{c:a._imports_0$5,d:e.o(L),e:T.value?"/static/my/select-icon1.png":"/static/my/select-icon2.png",f:e.o(S),g:$.value,h:e.o((e=>$.value=e.detail.value)),i:e.o(H),j:u.value},u.value?e.e({k:e.f(r.value,((a,l,o)=>({a:e.t(a),b:l,c:i.value===l?1:"",d:e.o((e=>(e=>{i.value=e,v.value=null,c.value=1,g.value=!0,b(1)})(l)),l)}))),l:a._imports_1$3,m:e.o(k),n:0===s.value.length&&1===c.value&&!m.value},0!==s.value.length||1!==c.value||m.value?{o:e.f(s.value,((a,l,o)=>({a:j(a),b:l,c:v.value&&v.value.id===a.id?1:"",d:e.o((e=>(e=>{v.value=e,console.log("选择模板:",e)})(a)),l)})))}:{},{p:s.value.length>0},s.value.length>0?e.e({q:m.value},(m.value||g.value,{}),{r:g.value}):{},{s:e.o(G),t:a._imports_2$1,v:e.o(U),w:e.o((()=>{})),x:e.o(U)}):{},{y:y.value},y.value?{z:a._imports_1$3,A:e.o(C),B:e.f(f.value,((a,l,o)=>({a:e.t(a),b:l,c:p.value===l?1:"",d:e.o((e=>(e=>{p.value=e})(l)),l)}))),C:e.f(q.value,((a,l,o)=>({a:e.t(a.name),b:l,c:h.value&&h.value.id===a.id?1:"",d:e.o((e=>(e=>{h.value=e})(a)),l)}))),D:e.o((()=>{})),E:e.o(z)}:{},{F:I.value},I.value?{G:_.value,H:e.t(M.value?"我同意":`我同意(${P.value}s)`),I:M.value?"":1,J:e.o(N),K:e.o((()=>{})),L:e.o(V)}:{})}},n=e._export_sfc(t,[["__scopeId","data-v-8cc1fb68"]]);wx.createPage(n);
