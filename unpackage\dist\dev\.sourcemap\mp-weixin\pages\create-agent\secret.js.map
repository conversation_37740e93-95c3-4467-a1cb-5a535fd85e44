{"version": 3, "file": "secret.js", "sources": ["pages/create-agent/secret.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY3JlYXRlLWFnZW50L3NlY3JldC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"secret-edit-page\">\r\n    <!-- 表单区域 -->\r\n    <view class=\"form-section\">\r\n      <!-- 动态生成秘钥输入框 -->\r\n      <view class=\"form-item\" v-for=\"item in secretKeyList\" :key=\"item.secretKeyType\">\r\n        <view class=\"label\">{{ item.secretKeyTypeText }}</view>\r\n        <view class=\"input-box\">\r\n          <input v-model=\"item.secretKey\" class=\"input\" :placeholder=\"`请输入${item.secretKeyTypeText}`\"\r\n            placeholder-class=\"placeholder\" maxlength=\"200\" />\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 保存按钮 -->\r\n    <view class=\"save-section\">\r\n      <view class=\"save-btn\" :class=\"{ disabled: saving }\" @tap=\"handleSave\">\r\n        <text class=\"save-text\">{{ saving ? '保存中...' : '保存' }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nimport { getSecretKeyListApi, saveSecretKeyApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 秘钥列表数据\r\nconst secretKeyList = ref([])\r\n\r\n// 获取秘钥列表\r\nconst getSecretKeyList = async () => {\r\n  try {\r\n    let res = await getSecretKeyListApi({\r\n      merchantGuid: userStore.merchantGuid,\r\n    })\r\n\r\n    if (res.code === 0 && res.data && res.data.length > 0) {\r\n      // 直接使用返回的数据，确保每个项都有secretKey字段\r\n      secretKeyList.value = res.data.map(item => ({\r\n        ...item,\r\n        secretKey: item.secretKey || '' // 如果没有secretKey则设为空字符串\r\n      }))\r\n    }\r\n  } catch (error) {\r\n    console.error('获取秘钥列表失败:', error)\r\n    uni.showToast({\r\n      title: '获取秘钥失败',\r\n      icon: 'none'\r\n    })\r\n  }\r\n}\r\n\r\nconst saving = ref(false)\r\n\r\n// 保存秘钥\r\nconst handleSave = async () => {\r\n  // 验证是否至少有一个秘钥被填写\r\n  const hasValidKey = secretKeyList.value.some(item => item.secretKey && item.secretKey.trim())\r\n\r\n  if (!hasValidKey) {\r\n    uni.showToast({\r\n      title: '请至少输入一个秘钥',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  saving.value = true\r\n\r\n  try {\r\n    // 只保存有值的秘钥\r\n    const saveSecretKeyList = secretKeyList.value\r\n      .filter(item => item.secretKey && item.secretKey.trim())\r\n      .map(item => ({\r\n        secretKeyType: item.secretKeyType,\r\n        secretKey: item.secretKey.trim()\r\n      }))\r\n\r\n    const saveData = {\r\n      merchantGuid: userStore.merchantGuid,\r\n      saveSecretKeyList\r\n    }\r\n\r\n    let res = await saveSecretKeyApi(saveData)\r\n\r\n    if (res.code === 0) {\r\n      uni.showToast({\r\n        title: '保存成功',\r\n        icon: 'success'\r\n      })\r\n\r\n      // 延迟返回上一页\r\n      setTimeout(() => {\r\n        uni.navigateBack()\r\n      }, 1500)\r\n    } else {\r\n      uni.showToast({\r\n        title: res.msg || '保存失败',\r\n        icon: 'none'\r\n      })\r\n    }\r\n  } catch (error) {\r\n    console.error('保存秘钥失败:', error)\r\n    uni.showToast({\r\n      title: '保存失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    saving.value = false\r\n  }\r\n}\r\n\r\n// 初始化秘钥配置\r\nonMounted(() => {\r\n  getSecretKeyList()\r\n})\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.secret-edit-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.form-section {\r\n  padding: 20px 32rpx;\r\n\r\n  .form-item {\r\n\r\n    .input-box {\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 20rpx 16rpx;\r\n      background: #ffffff;\r\n      border-radius: 20rpx;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .label {\r\n      font-size: 28rpx;\r\n      color: #585858;\r\n      font-weight: 500;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .input {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      color: #1a1a1a;\r\n      height: 44rpx;\r\n      line-height: 44rpx;\r\n\r\n      &.placeholder {\r\n        color: #CCCCCC;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.save-section {\r\n  padding: 80rpx 32rpx;\r\n  padding-bottom: calc(80rpx + env(safe-area-inset-bottom));\r\n  /* Android兼容性修复 */\r\n  padding-bottom: 80rpx;\r\n\r\n  .save-btn {\r\n    width: 100%;\r\n    height: 96rpx;\r\n    background: #3478f6;\r\n    border-radius: 48rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: all 0.3s ease;\r\n\r\n    &.disabled {\r\n      background: #CCCCCC;\r\n    }\r\n\r\n    .save-text {\r\n      font-size: 32rpx;\r\n      color: #ffffff;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n/* 占位符样式 */\r\n.placeholder {\r\n  color: #CCCCCC !important;\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/create-agent/secret.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "getSecretKeyListApi", "uni", "saveSecretKeyApi", "onMounted"], "mappings": ";;;;;;;AA2BA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,gBAAgBC,cAAG,IAAC,EAAE;AAG5B,UAAM,mBAAmB,YAAY;AACnC,UAAI;AACF,YAAI,MAAM,MAAMC,8BAAoB;AAAA,UAClC,cAAc,UAAU;AAAA,QAC9B,CAAK;AAED,YAAI,IAAI,SAAS,KAAK,IAAI,QAAQ,IAAI,KAAK,SAAS,GAAG;AAErD,wBAAc,QAAQ,IAAI,KAAK,IAAI,WAAS;AAAA,YAC1C,GAAG;AAAA,YACH,WAAW,KAAK,aAAa;AAAA;AAAA,UACrC,EAAQ;AAAA,QACH;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,SAASF,cAAG,IAAC,KAAK;AAGxB,UAAM,aAAa,YAAY;AAE7B,YAAM,cAAc,cAAc,MAAM,KAAK,UAAQ,KAAK,aAAa,KAAK,UAAU,KAAI,CAAE;AAE5F,UAAI,CAAC,aAAa;AAChBE,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,aAAO,QAAQ;AAEf,UAAI;AAEF,cAAM,oBAAoB,cAAc,MACrC,OAAO,UAAQ,KAAK,aAAa,KAAK,UAAU,MAAM,EACtD,IAAI,WAAS;AAAA,UACZ,eAAe,KAAK;AAAA,UACpB,WAAW,KAAK,UAAU,KAAM;AAAA,QACxC,EAAQ;AAEJ,cAAM,WAAW;AAAA,UACf,cAAc,UAAU;AAAA,UACxB;AAAA,QACD;AAED,YAAI,MAAM,MAAMC,UAAgB,iBAAC,QAAQ;AAEzC,YAAI,IAAI,SAAS,GAAG;AAClBD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAGD,qBAAW,MAAM;AACfA,0BAAAA,MAAI,aAAc;AAAA,UACnB,GAAE,IAAI;AAAA,QACb,OAAW;AACLA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACd,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,wCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,eAAO,QAAQ;AAAA,MAChB;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;AACd,uBAAkB;AAAA,IACpB,CAAC;;;;;;;;;;;;;;;;;;;;ACrHD,GAAG,WAAW,eAAe;"}