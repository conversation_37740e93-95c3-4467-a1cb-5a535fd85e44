{"version": 3, "file": "index.js", "sources": ["pages/create-agent/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY3JlYXRlLWFnZW50L2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"create-agent-page\">\r\n    <scroll-view class=\"page-scroll\" scroll-y>\r\n      <!-- 头像区域 -->\r\n      <view class=\"avatar-section\">\r\n        <view class=\"avatar-container\" @tap=\"chooseAvatar\">\r\n          <image :src=\"agentForm.agentAvatar\" class=\"avatar\" mode=\"aspectFill\" />\r\n          <view class=\"avatar-add\" v-if=\"!generating\">\r\n            <image src=\"@/static/msg/<EMAIL>\" class=\"add-icon\" mode=\"aspectFit\" />\r\n          </view>\r\n          <!-- 生成中的加载效果 -->\r\n          <view class=\"avatar-loading\" v-if=\"generating\">\r\n            <view class=\"loading-spinner\"></view>\r\n          </view>\r\n        </view>\r\n        <text class=\"avatar-label\" @click=\"onCreateLogo\" :class=\"{ 'disabled': generating }\">\r\n          {{ generating ? 'AI生成中...' : 'AI生成形象' }}\r\n        </text>\r\n        <view class=\"secret-bnt\" @click=\"onSecret\">秘钥管理</view>\r\n      </view>\r\n\r\n      <!-- 表单区域 -->\r\n      <view class=\"form-section\">\r\n\r\n        <!-- 选择分类 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">选择分类:</text>\r\n          <view class=\"category-selector\" @tap=\"showCategoryPicker\">\r\n            <text class=\"category-text\" :class=\"{ 'placeholder': !selectedCategory }\">\r\n              {{ selectedCategory ? selectedCategory.categoryName : '请选择分类' }}\r\n            </text>\r\n            <!-- <image src=\"/static/placeholder-arrow-down.png\" class=\"arrow-icon\" mode=\"aspectFit\" /> -->\r\n          </view>\r\n        </view>\r\n        <!-- 名称 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">名称:</text>\r\n          <input v-model=\"agentForm.agentName\" class=\"input\" placeholder=\"输入名称\" placeholder-class=\"placeholder\"\r\n            maxlength=\"50\" />\r\n        </view>\r\n\r\n        <!-- 智能体描述 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">智能体描述:</text>\r\n          <textarea v-model=\"agentForm.agentDesc\" class=\"textarea\" placeholder=\"输入描述\" placeholder-class=\"placeholder\"\r\n            maxlength=\"500\" />\r\n        </view>\r\n\r\n        <!-- 选择智能体类型 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">选择智能体类型:</text>\r\n          <view class=\"radio-group\">\r\n            <view class=\"radio-item\" v-for=\"type in agentTypes\" :key=\"type.value\" @tap=\"selectAgentType(type.value)\">\r\n              <view class=\"radio\" :class=\"{ 'checked': agentForm.agentType === type.value }\">\r\n                <view class=\"radio-inner\" v-if=\"agentForm.agentType === type.value\"></view>\r\n              </view>\r\n              <text class=\"radio-text\">{{ type.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 角色设定提示词 -->\r\n        <view class=\"form-item\" v-if=\"agentForm.agentType === 1\">\r\n          <text class=\"label\">角色设定提示词:</text>\r\n          <textarea v-model=\"agentForm.promptContent\" class=\"textarea large\" placeholder=\"输入提示词\"\r\n            placeholder-class=\"placeholder\" maxlength=\"2000\" />\r\n        </view>\r\n        <!-- 密钥读取类型 -->\r\n        <view class=\"form-item\" v-if=\"agentForm.agentType !== 1\">\r\n          <text class=\"label\">密钥读取类型:</text>\r\n          <view class=\"radio-group\">\r\n            <view class=\"radio-item\" v-for=\"visibility in secretTypes\" :key=\"visibility.value\"\r\n              @tap=\"selectSecretType(visibility.value)\">\r\n              <view class=\"radio\" :class=\"{ 'checked': agentForm.agentConfig.secret_key_type === visibility.value }\">\r\n                <view class=\"radio-inner\" v-if=\"agentForm.agentConfig.secret_key_type === visibility.value\"></view>\r\n              </view>\r\n              <text class=\"radio-text\">{{ visibility.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <!-- 智能体密码 -->\r\n        <view class=\"form-item\" v-if=\"agentForm.agentType === 3\">\r\n          <text class=\"label\">扣子智能体ID:</text>\r\n          <input v-model=\"agentForm.agentConfig.coze_sign\" class=\"input\" placeholder=\"输入密码\"\r\n            placeholder-class=\"placeholder\" />\r\n        </view>\r\n\r\n        <!-- 智能体密码 -->\r\n        <view class=\"form-item\" v-if=\"agentForm.agentType !== 1\">\r\n          <text class=\"label\">智能体秘钥:</text>\r\n          <input v-model=\"agentForm.agentConfig.secret_token\" class=\"input\" placeholder=\"输入秘钥\"\r\n            placeholder-class=\"placeholder\" />\r\n        </view>\r\n        <!-- 自定义部署地址 -->\r\n        <view class=\"form-item\" v-if=\"agentForm.agentType === 2\">\r\n          <text class=\"label\">dify自定义部署地址:</text>\r\n          <input v-model=\"agentForm.agentConfig.deploy_address\" class=\"input\" placeholder=\"输入地址\"\r\n            placeholder-class=\"placeholder\" />\r\n        </view>\r\n\r\n\r\n        <!-- 开场白 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">开场白:</text>\r\n          <input v-model=\"agentForm.welcomeMessage\" class=\"input\" placeholder=\"输入开场白\" placeholder-class=\"placeholder\" />\r\n        </view>\r\n\r\n        <!-- 引导问题列表 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">引导问题列表:</text>\r\n          <view class=\"question-list\">\r\n            <view class=\"question-item\" v-for=\"(question, index) in agentForm.commonQuestions\" :key=\"index\">\r\n              <input v-model=\"agentForm.commonQuestions[index]\" class=\"question-input\"\r\n                :placeholder=\"`请输入引导问题${index + 1}`\" placeholder-class=\"placeholder\" />\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 选择智能体类型 -->\r\n        <view class=\"form-item\">\r\n          <text class=\"label\">选择智能体类型:</text>\r\n          <view class=\"radio-group\">\r\n            <view class=\"radio-item\" v-for=\"visibility in visibilityTypes\" :key=\"visibility.value\"\r\n              @tap=\"selectVisibility(visibility.value)\">\r\n              <view class=\"radio\" :class=\"{ 'checked': agentForm.isPaid === visibility.value }\">\r\n                <view class=\"radio-inner\" v-if=\"agentForm.isPaid === visibility.value\"></view>\r\n              </view>\r\n              <text class=\"radio-text\">{{ visibility.label }}</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 收费金额 -->\r\n        <view class=\"form-item\" v-if=\"agentForm.isPaid === 1\">\r\n          <text class=\"label\">收费金额:</text>\r\n          <input v-model=\"agentForm.price\" class=\"input\" placeholder=\"输入金额\" placeholder-class=\"placeholder\"\r\n            type=\"digit\" />\r\n        </view>\r\n\r\n        <view class=\"form-item\" v-if=\"agentForm.isPaid === 1\">\r\n          <text class=\"label\">试用聊天次数:</text>\r\n          <input v-model=\"agentForm.trial_chat_count\" class=\"input\" placeholder=\"试用聊天次数\" placeholder-class=\"placeholder\"\r\n            type=\"digit\" />\r\n        </view>\r\n\r\n        <!-- 底部说明 -->\r\n        <view class=\"form-note\">\r\n          <text class=\"note-text\">注意：智能体不得违反相关法律法规，禁止涉及政治敏感话题，详情请查看《平台协议》</text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 创建按钮 -->\r\n      <view class=\"create-section\">\r\n        <view class=\"create-btn\" @tap=\"handleCreate\" :class=\"{ 'disabled': creating }\">\r\n          <text class=\"create-text\">{{ creating ? (isEditMode ? '更新中...' : '创建中...') : (isEditMode ? '完成' : '创建AI智能体')\r\n          }}</text>\r\n        </view>\r\n      </view>\r\n    </scroll-view>\r\n\r\n    <!-- 分类选择弹窗 -->\r\n    <view v-if=\"showCategoryModal\" class=\"category-modal\" @tap=\"closeCategoryModal\">\r\n      <view class=\"modal-content\" @tap.stop>\r\n        <!-- 弹窗头部 -->\r\n        <view class=\"modal-header\">\r\n          <text class=\"modal-title\">选择分类</text>\r\n          <!-- <view class=\"close-btn\" @tap=\"closeCategoryModal\">\r\n            <image src=\"/static/placeholder-close.png\" class=\"close-icon\" mode=\"aspectFit\" />\r\n          </view> -->\r\n        </view>\r\n\r\n        <!-- 搜索框 -->\r\n        <view class=\"search-section\">\r\n          <view class=\"search-box\">\r\n            <image src=\"/static/placeholder-search.png\" class=\"search-icon\" mode=\"aspectFit\" />\r\n            <input v-model=\"searchKeyword\" class=\"search-input\" placeholder=\"搜索分类\" placeholder-class=\"placeholder\"\r\n              @input=\"handleSearch\" />\r\n            <view v-if=\"searchKeyword\" class=\"clear-btn\" @tap=\"clearSearch\">\r\n              <image src=\"/static/placeholder-close.png\" class=\"clear-icon\" mode=\"aspectFit\" />\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- 分类列表 -->\r\n        <scroll-view class=\"category-list\" scroll-y>\r\n          <view v-for=\"(category, index) in filteredCategories\" :key=\"category.guid\" class=\"category-item\"\r\n            @tap=\"selectTempCategory(category)\">\r\n            <view class=\"radio\" :class=\"{ 'checked': tempSelectedCategory?.guid === category.guid }\">\r\n              <view class=\"radio-inner\" v-if=\"tempSelectedCategory?.guid === category.guid\"></view>\r\n            </view>\r\n            <text class=\"category-name\">{{ category.categoryName }}</text>\r\n          </view>\r\n\r\n          <!-- 空状态 -->\r\n          <view v-if=\"filteredCategories.length === 0\" class=\"empty-state\">\r\n            <text class=\"empty-text\">暂无匹配的分类</text>\r\n          </view>\r\n        </scroll-view>\r\n\r\n        <!-- 确认按钮 -->\r\n        <view class=\"modal-footer\">\r\n          <view class=\"cancel-btn\" @tap=\"closeCategoryModal\">\r\n            <text class=\"cancel-text\">取消</text>\r\n          </view>\r\n          <view class=\"confirm-btn\" @tap=\"confirmSelectCategory\" :class=\"{ 'disabled': !tempSelectedCategory }\">\r\n            <text class=\"confirm-text\">确认</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { onLoad } from '@dcloudio/uni-app'\r\nimport { createAgentApi, getCategoryListApi, generateAvatarApi, getMyDetailApi, updateMyAgentApi } from '@/api/index.js'\r\nimport { useUserStore } from '@/stores/user.js'\r\nimport { updataFileFun } from '@/api/common.js'\r\n\r\nconst userStore = useUserStore()\r\n\r\n// 表单数据\r\nconst agentForm = reactive({\r\n  merchantGuid: userStore.merchantGuid,\r\n  agentName: '',\r\n  agentType: 1, // 默认内部类型\r\n  categoryGuid: '', // 需要从分类列表获取\r\n  promptContent: '',\r\n  agentDesc: '',\r\n  agentAvatar: '',\r\n  isPaid: 0, // 默认免费\r\n  price: '',\r\n  trial_chat_count: 0, // 试用聊天次数\r\n  isPublic: 1, // 默认公开\r\n  agentConfig: {\r\n    // model: 'gpt-4',\r\n    // temperature: 0.7,\r\n    // max_tokens: 2000,\r\n    // api_key: '',\r\n    // workflow_id: '',\r\n    deploy_address: '',\r\n    coze_sign: '',\r\n    secret_token: '',\r\n    secret_key_type: 'default'\r\n  },\r\n  knowledgeBaseIds: [],\r\n  welcomeMessage: '',\r\n  commonQuestions: ['', '', ''], // 默认3个问题\r\n})\r\n\r\n// 加载状态\r\nconst creating = ref(false)\r\nconst uploading = ref(false)\r\nconst generating = ref(false) // AI生成头像状态\r\n\r\n// 编辑模式相关\r\nconst isEditMode = ref(false)\r\nconst editGuid = ref('')\r\n\r\n// 分类相关\r\nconst selectedCategory = ref(null)\r\nconst categoryList = ref([])\r\nconst showCategoryModal = ref(false)\r\nconst tempSelectedCategory = ref(null)\r\nconst searchKeyword = ref('')\r\n\r\n// 计算属性 - 过滤后的分类列表\r\nconst filteredCategories = computed(() => {\r\n  if (!searchKeyword.value) {\r\n    return categoryList.value\r\n  }\r\n  return categoryList.value.filter(item =>\r\n    item.categoryName.toLowerCase().includes(searchKeyword.value.toLowerCase())\r\n  )\r\n})\r\n\r\n// 智能体类型选项\r\nconst agentTypes = ref([\r\n  { label: '内部', value: 1 },\r\n  { label: 'dify', value: 2 },\r\n  { label: 'coze', value: 3 },\r\n  // { label: '阿里云百炼', value: 4 }\r\n])\r\n\r\n// 秘钥类型\r\nconst secretTypes = ref([\r\n  { label: '自定义输入', value: 'default' },\r\n  { label: '读取用户保存密钥', value: 'user' }\r\n])\r\nlet secretType = ref('default')\r\n// 可见性类型选项\r\nconst visibilityTypes = ref([\r\n  { label: '免费', value: 0 },\r\n  { label: '付费', value: 1 }\r\n])\r\n//跳转秘钥管理\r\nconst onSecret = () => {\r\n  console.log('跳转秘钥管理')\r\n  uni.navigateTo({\r\n    url: '/pages/create-agent/secret'\r\n  })\r\n}\r\n\r\n// 选择头像\r\nconst chooseAvatar = () => {\r\n  if (uploading.value) return\r\n\r\n  uni.chooseImage({\r\n    count: 1,\r\n    sizeType: ['compressed'],\r\n    sourceType: ['album', 'camera'],\r\n    success: async (res) => {\r\n      const tempFilePath = res.tempFilePaths[0]\r\n      await uploadAvatar(tempFilePath)\r\n    },\r\n    fail: (err) => {\r\n      console.error('选择图片失败:', err)\r\n    }\r\n  })\r\n}\r\n\r\n// 上传头像\r\nconst uploadAvatar = async (filePath) => {\r\n  try {\r\n    uploading.value = true\r\n    uni.showLoading({\r\n      title: '上传中...',\r\n      mask: true\r\n    })\r\n\r\n    const uploadRes = await updataFileFun(filePath)\r\n    const result = JSON.parse(uploadRes.data)\r\n\r\n    if (result.code === 0) {\r\n      agentForm.agentAvatar = result.data\r\n      uni.showToast({\r\n        title: '头像上传成功',\r\n        icon: 'success'\r\n      })\r\n    } else {\r\n      throw new Error(result.msg || '上传失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('上传头像失败:', error)\r\n    uni.showToast({\r\n      title: '上传失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    uploading.value = false\r\n    uni.hideLoading()\r\n  }\r\n}\r\n// AI生成头像\r\nconst onCreateLogo = async () => {\r\n  // 验证必填字段\r\n  if (!agentForm.agentName.trim()) {\r\n    uni.showToast({\r\n      title: '请先输入智能体名称',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!agentForm.agentDesc.trim()) {\r\n    uni.showToast({\r\n      title: '请先输入智能体描述',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (generating.value) return\r\n\r\n  try {\r\n    generating.value = true\r\n    uni.showLoading({\r\n      title: 'AI生成中...',\r\n      mask: true\r\n    })\r\n\r\n    const generateData = {\r\n      merchantGuid: userStore.merchantGuid,\r\n      agentName: agentForm.agentName.trim(),\r\n      agentDesc: agentForm.agentDesc.trim()\r\n    }\r\n\r\n    const res = await generateAvatarApi(generateData)\r\n\r\n    if (res.code === 0) {\r\n      agentForm.agentAvatar = res.data.data.imageUrl\r\n      uni.showToast({\r\n        title: '头像生成成功',\r\n        icon: 'success'\r\n      })\r\n    } else {\r\n      throw new Error(res.msg || '生成失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('AI生成头像失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '生成失败',\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    generating.value = false\r\n    uni.hideLoading()\r\n  }\r\n}\r\n// 选择智能体类型\r\nconst selectAgentType = (value) => {\r\n  agentForm.agentType = value\r\n}\r\n\r\n// 选择可见性类型\r\nconst selectVisibility = (value) => {\r\n  agentForm.isPaid = value\r\n}\r\n\r\nconst selectSecretType = (value) => {\r\n  agentForm.agentConfig.secret_key_type = value\r\n}\r\n// 获取分类列表\r\nconst getCategoryList = async () => {\r\n  try {\r\n    const res = await getCategoryListApi({\r\n      merchantGuid: userStore.merchantGuid\r\n    })\r\n    if (res.code === 0) {\r\n      categoryList.value = res.data || []\r\n    }\r\n  } catch (error) {\r\n    console.error('获取分类列表失败:', error)\r\n  }\r\n}\r\n\r\n// 显示分类选择器\r\nconst showCategoryPicker = () => {\r\n  if (categoryList.value.length === 0) {\r\n    getCategoryList()\r\n  }\r\n  // 打开弹窗时重置临时选择状态，不显示当前已选项\r\n  // tempSelectedCategory.value = null\r\n  showCategoryModal.value = true\r\n}\r\n\r\n// 关闭分类选择弹窗\r\nconst closeCategoryModal = () => {\r\n  showCategoryModal.value = false\r\n  searchKeyword.value = ''\r\n  // tempSelectedCategory.value = null\r\n}\r\n\r\n// 搜索处理\r\nconst handleSearch = () => {\r\n  // 搜索时保持当前选中状态，不重置\r\n  // 用户可以在搜索结果中看到当前选中项\r\n}\r\n\r\n// 清除搜索\r\nconst clearSearch = () => {\r\n  searchKeyword.value = ''\r\n  // 保持当前临时选中状态，不重置为原来的选中状态\r\n}\r\n\r\nconst selectTempCategory = (category) => {\r\n  // 直接选中当前分类（自动取消之前的选中）\r\n  tempSelectedCategory.value = category\r\n}\r\n\r\n// 确认选择分类\r\nconst confirmSelectCategory = () => {\r\n  if (!tempSelectedCategory.value) {\r\n    uni.showToast({\r\n      title: '请选择分类',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  selectedCategory.value = tempSelectedCategory.value\r\n  agentForm.categoryGuid = tempSelectedCategory.value.guid\r\n  closeCategoryModal()\r\n}\r\n\r\n// 获取智能体详情\r\nconst getAgentDetail = async (guid) => {\r\n  try {\r\n    uni.showLoading({\r\n      title: '加载中...',\r\n      mask: true\r\n    })\r\n\r\n    const res = await getMyDetailApi({ guid })\r\n    if (res.code === 0) {\r\n      const agentData = res.data\r\n\r\n      // 填充表单数据\r\n      agentForm.agentName = agentData.agentName || ''\r\n      agentForm.agentType = agentData.agentType || 1\r\n      agentForm.categoryGuid = agentData.categoryGuid || ''\r\n      agentForm.promptContent = agentData.promptContent || ''\r\n      agentForm.agentDesc = agentData.agentDesc || ''\r\n      agentForm.agentAvatar = agentData.agentAvatar || ''\r\n      agentForm.isPaid = agentData.isPaid || 0\r\n      agentForm.price = agentData.price || ''\r\n      agentForm.trial_chat_count = agentData.trialChatCount || 0 // 试用聊天次数\r\n      agentForm.isPublic = agentData.isPublic || 1\r\n      agentForm.agentConfig = agentData.agentConfig || { secret_token: '', secret_key_type: 'default', deploy_address: '' }\r\n      agentForm.knowledgeBaseIds = agentData.knowledgeBaseIds || []\r\n      agentForm.welcomeMessage = agentData.welcomeMessage || ''\r\n      agentForm.commonQuestions = agentData.commonQuestions.length > 0 ? agentData.commonQuestions : ['', '', '']\r\n      // 设置选中的分类\r\n      if (agentData.category) {\r\n        selectedCategory.value = agentData.category\r\n      }\r\n\r\n      // 存储编辑的guid\r\n      editGuid.value = guid\r\n    } else {\r\n      throw new Error(res.msg || '获取智能体详情失败')\r\n    }\r\n  } catch (error) {\r\n    console.error('获取智能体详情失败:', error)\r\n    uni.showToast({\r\n      title: error.message || '获取智能体详情失败',\r\n      icon: 'none'\r\n    })\r\n    // 获取失败时返回上一页\r\n    setTimeout(() => {\r\n      uni.navigateBack()\r\n    }, 1500)\r\n  } finally {\r\n    uni.hideLoading()\r\n  }\r\n}\r\n\r\n// 页面加载时检查参数\r\nonLoad((options) => {\r\n  if (options.guid) {\r\n    isEditMode.value = true\r\n    getAgentDetail(options.guid)\r\n  }\r\n})\r\n\r\n// 页面初始化\r\nonMounted(() => {\r\n  getCategoryList()\r\n})\r\n\r\n// 创建智能体\r\nconst handleCreate = async () => {\r\n  if (creating.value) return\r\n\r\n  // 表单验证\r\n  if (!agentForm.categoryGuid) {\r\n    uni.showToast({\r\n      title: '请选择分类',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  if (!agentForm.agentName.trim()) {\r\n    uni.showToast({\r\n      title: '请输入智能体名称',\r\n      icon: 'none'\r\n    })\r\n    return\r\n  }\r\n\r\n  // if (!agentForm.promptContent.trim()) {\r\n  //   uni.showToast({\r\n  //     title: '请输入角色设定提示词',\r\n  //     icon: 'none'\r\n  //   })\r\n  //   return\r\n  // }\r\n\r\n  try {\r\n    creating.value = true\r\n    uni.showLoading({\r\n      title: isEditMode.value ? '更新中...' : '创建中...',\r\n      mask: true\r\n    })\r\n\r\n    // 处理价格\r\n    if (agentForm.isPaid === 1 && !agentForm.price) {\r\n      uni.showToast({\r\n        title: '付费智能体请输入价格',\r\n        icon: 'none'\r\n      })\r\n      creating.value = false\r\n      uni.hideLoading()\r\n      return\r\n    }\r\n\r\n    // 过滤空的引导问题\r\n    const filteredQuestions = agentForm.commonQuestions.filter(q => q.trim())\r\n\r\n    const submitData = {\r\n      agentName: agentForm.agentName.trim(),\r\n      agentType: agentForm.agentType,\r\n      categoryGuid: agentForm.categoryGuid,\r\n      promptContent: agentForm.promptContent.trim(),\r\n      agentDesc: agentForm.agentDesc.trim(),\r\n      agentAvatar: agentForm.agentAvatar,\r\n      isPaid: agentForm.isPaid,\r\n      price: agentForm.isPaid === 1 ? parseFloat(agentForm.price) : 0,\r\n      isPublic: 1, // 暂时都设为公开\r\n      trial_chat_count: agentForm.trial_chat_count, // 试用聊天次数\r\n      agentConfig: agentForm.agentConfig,\r\n      knowledgeBaseIds: agentForm.knowledgeBaseIds,\r\n      welcomeMessage: agentForm.welcomeMessage.trim(),\r\n      commonQuestions: filteredQuestions\r\n    }\r\n\r\n    // 根据模式选择不同的参数和API\r\n    if (isEditMode.value) {\r\n      submitData.guid = editGuid.value\r\n      console.log('更新智能体数据:', submitData)\r\n      const res = await updateMyAgentApi(submitData)\r\n      if (res.code === 0) {\r\n        uni.showToast({\r\n          title: '更新成功',\r\n          icon: 'success'\r\n        })\r\n        // 延迟返回上一页\r\n        setTimeout(() => {\r\n          uni.navigateBack()\r\n        }, 1500)\r\n      } else {\r\n        throw new Error(res.msg || '更新失败')\r\n      }\r\n    } else {\r\n      submitData.merchantGuid = agentForm.merchantGuid\r\n      console.log('创建智能体数据:', submitData)\r\n      const res = await createAgentApi(submitData)\r\n      if (res.code === 0) {\r\n        uni.showToast({\r\n          title: '创建成功',\r\n          icon: 'success'\r\n        })\r\n        // 延迟返回上一页\r\n        setTimeout(() => {\r\n          uni.navigateBack()\r\n        }, 1500)\r\n      } else {\r\n        throw new Error(res.msg || '创建失败')\r\n      }\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error(isEditMode.value ? '更新失败:' : '创建失败:', error)\r\n    uni.showToast({\r\n      title: error.msg || (isEditMode.value ? '更新失败' : '创建失败'),\r\n      icon: 'none'\r\n    })\r\n  } finally {\r\n    creating.value = false\r\n    uni.hideLoading()\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.create-agent-page {\r\n  background: #F5F5F5;\r\n  min-height: 100vh;\r\n}\r\n\r\n.page-scroll {\r\n  height: 100vh;\r\n}\r\n\r\n.avatar-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 60rpx 0 40rpx;\r\n  position: relative;\r\n\r\n  .avatar-container {\r\n    position: relative;\r\n    width: 160rpx;\r\n    height: 160rpx;\r\n    margin-bottom: 16rpx;\r\n\r\n    .avatar {\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 50%;\r\n      background: #f0f0f0;\r\n    }\r\n\r\n    .avatar-add {\r\n      position: absolute;\r\n      bottom: 0;\r\n      right: 0;\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      background: #3478f6;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: 4rpx solid #ffffff;\r\n\r\n      .add-icon {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n\r\n    .avatar-loading {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      background: rgba(0, 0, 0, 0.6);\r\n      border-radius: 50%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .loading-spinner {\r\n        width: 40rpx;\r\n        height: 40rpx;\r\n        border: 4rpx solid rgba(255, 255, 255, 0.3);\r\n        border-top: 4rpx solid #ffffff;\r\n        border-radius: 50%;\r\n        animation: spin 1s linear infinite;\r\n      }\r\n    }\r\n  }\r\n\r\n  .avatar-label {\r\n    font-size: 26rpx;\r\n    color: #3478f6;\r\n    background-color: #FFFFFF;\r\n    border-radius: 35rpx;\r\n    padding: 10rpx 20rpx;\r\n    transition: all 0.3s ease;\r\n\r\n    &.disabled {\r\n      color: #CCCCCC;\r\n      background-color: #F5F5F5;\r\n    }\r\n  }\r\n\r\n  .secret-bnt {\r\n    font-size: 26rpx;\r\n    color: #fff;\r\n    background-color: #3478f6;\r\n    border-radius: 35rpx;\r\n    padding: 10rpx 20rpx;\r\n    transition: all 0.3s ease;\r\n    position: absolute;\r\n    right: 10px;\r\n    top: 10px;\r\n  }\r\n}\r\n\r\n.form-section {\r\n  // background: #ffffff;\r\n  margin-top: 20rpx;\r\n  padding: 0 32rpx;\r\n\r\n  .form-item {\r\n    padding: 30rpx;\r\n    background: #fff;\r\n    margin-bottom: 30rpx;\r\n    border-radius: 16rpx;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    .label {\r\n      font-size: 30rpx;\r\n      color: #333333;\r\n      font-weight: 500;\r\n      margin-bottom: 24rpx;\r\n      display: block;\r\n    }\r\n\r\n    .input {\r\n      width: 100%;\r\n      height: 88rpx;\r\n      font-size: 28rpx;\r\n      color: #333;\r\n      background: #F8F9FA;\r\n      border-radius: 12rpx;\r\n      padding: 0 24rpx;\r\n      border: none;\r\n      box-sizing: border-box;\r\n      line-height: 88rpx;\r\n    }\r\n\r\n    .category-selector {\r\n      width: 100%;\r\n      height: 88rpx;\r\n      background: #F8F9FA;\r\n      border-radius: 12rpx;\r\n      padding: 0 24rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      box-sizing: border-box;\r\n\r\n      .category-text {\r\n        font-size: 28rpx;\r\n        color: #1a1a1a;\r\n        flex: 1;\r\n        line-height: 88rpx;\r\n\r\n        &.placeholder {\r\n          color: #CCCCCC;\r\n        }\r\n      }\r\n\r\n      .arrow-icon {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n        margin-left: 16rpx;\r\n      }\r\n    }\r\n\r\n    .textarea {\r\n      width: 100%;\r\n      min-height: 120rpx;\r\n      font-size: 28rpx;\r\n      color: #1a1a1a;\r\n      background: #F8F9FA;\r\n      border-radius: 12rpx;\r\n      padding: 24rpx;\r\n      border: none;\r\n      box-sizing: border-box;\r\n      line-height: 1.5;\r\n\r\n      &.large {\r\n        min-height: 200rpx;\r\n      }\r\n    }\r\n\r\n    .radio-group {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 32rpx;\r\n\r\n      .radio-item {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .radio {\r\n          width: 32rpx;\r\n          height: 32rpx;\r\n          border: 2rpx solid #CCCCCC;\r\n          border-radius: 50%;\r\n          margin-right: 16rpx;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &.checked {\r\n            border-color: #3478f6;\r\n\r\n            .radio-inner {\r\n              width: 16rpx;\r\n              height: 16rpx;\r\n              background: #3478f6;\r\n              border-radius: 50%;\r\n            }\r\n          }\r\n        }\r\n\r\n        .radio-text {\r\n          font-size: 28rpx;\r\n          color: #1a1a1a;\r\n        }\r\n      }\r\n    }\r\n\r\n    .question-list {\r\n      .question-item {\r\n        margin-bottom: 24rpx;\r\n\r\n        &:last-child {\r\n          margin-bottom: 0;\r\n        }\r\n\r\n        .question-input {\r\n          width: 100%;\r\n          height: 80rpx;\r\n          font-size: 28rpx;\r\n          color: #1a1a1a;\r\n          background: #F8F9FA;\r\n          border-radius: 12rpx;\r\n          padding: 0 20rpx;\r\n          border: none;\r\n          box-sizing: border-box;\r\n          line-height: 80rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-note {\r\n    padding: 32rpx 0;\r\n\r\n    .note-text {\r\n      font-size: 24rpx;\r\n      color: #999999;\r\n      line-height: 1.6;\r\n    }\r\n  }\r\n}\r\n\r\n.create-section {\r\n  padding: 40rpx 32rpx;\r\n  padding-bottom: calc(40rpx + env(safe-area-inset-bottom));\r\n  background: #F5F5F5;\r\n\r\n  .create-btn {\r\n    width: 100%;\r\n    height: 96rpx;\r\n    background: #3478f6;\r\n    border-radius: 48rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: all 0.3s ease;\r\n\r\n    &.disabled {\r\n      background: #CCCCCC;\r\n    }\r\n\r\n    .create-text {\r\n      font-size: 32rpx;\r\n      color: #ffffff;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n/* 占位符样式 */\r\n.placeholder {\r\n  color: #CCCCCC !important;\r\n}\r\n\r\n/* 小程序input组件特殊样式 */\r\ninput {\r\n  outline: none;\r\n}\r\n\r\ntextarea {\r\n  outline: none;\r\n}\r\n\r\n/* 分类选择弹窗样式 */\r\n.category-modal {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 9999;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80rpx 40rpx;\r\n  box-sizing: border-box;\r\n\r\n  .modal-content {\r\n    width: 100%;\r\n    max-height: 80%;\r\n    background: #ffffff;\r\n    border-radius: 24rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .modal-header {\r\n    padding: 40rpx 32rpx 24rpx;\r\n    border-bottom: 1px solid #F0F0F0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .modal-title {\r\n      font-size: 36rpx;\r\n      font-weight: 600;\r\n      color: #1a1a1a;\r\n    }\r\n\r\n    .close-btn {\r\n      width: 48rpx;\r\n      height: 48rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n      .close-icon {\r\n        width: 24rpx;\r\n        height: 24rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .search-section {\r\n    padding: 24rpx 32rpx;\r\n    border-bottom: 1px solid #F0F0F0;\r\n\r\n    .search-box {\r\n      position: relative;\r\n      background: #F8F9FA;\r\n      border-radius: 24rpx;\r\n      padding: 0 48rpx 0 80rpx;\r\n      height: 72rpx;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .search-icon {\r\n        position: absolute;\r\n        left: 24rpx;\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n      }\r\n\r\n      .search-input {\r\n        flex: 1;\r\n        font-size: 28rpx;\r\n        color: #1a1a1a;\r\n        height: 72rpx;\r\n        line-height: 72rpx;\r\n      }\r\n\r\n      .clear-btn {\r\n        position: absolute;\r\n        right: 16rpx;\r\n        width: 48rpx;\r\n        height: 48rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        .clear-icon {\r\n          width: 24rpx;\r\n          height: 24rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .category-list {\r\n    flex: 1;\r\n    min-height: 400rpx;\r\n    max-height: 600rpx;\r\n\r\n    .category-item {\r\n      padding: 32rpx;\r\n      border-bottom: 1px solid #F0F0F0;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .radio {\r\n        width: 32rpx;\r\n        height: 32rpx;\r\n        border: 2rpx solid #CCCCCC;\r\n        border-radius: 50%;\r\n        margin-right: 24rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        flex-shrink: 0;\r\n\r\n        &.checked {\r\n          border-color: #3478f6;\r\n\r\n          .radio-inner {\r\n            width: 16rpx;\r\n            height: 16rpx;\r\n            background: #3478f6;\r\n            border-radius: 50%;\r\n          }\r\n        }\r\n      }\r\n\r\n      .category-name {\r\n        font-size: 32rpx;\r\n        color: #1a1a1a;\r\n        flex: 1;\r\n      }\r\n    }\r\n\r\n    .empty-state {\r\n      padding: 120rpx 32rpx;\r\n      text-align: center;\r\n\r\n      .empty-text {\r\n        font-size: 28rpx;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n\r\n  .modal-footer {\r\n    padding: 32rpx;\r\n    border-top: 1px solid #F0F0F0;\r\n    display: flex;\r\n    gap: 24rpx;\r\n\r\n    .cancel-btn,\r\n    .confirm-btn {\r\n      flex: 1;\r\n      height: 80rpx;\r\n      border-radius: 40rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      transition: all 0.3s ease;\r\n    }\r\n\r\n    .cancel-btn {\r\n      background: #F8F9FA;\r\n      border: 1px solid #E5E5E5;\r\n\r\n      .cancel-text {\r\n        font-size: 32rpx;\r\n        color: #666666;\r\n      }\r\n    }\r\n\r\n    .confirm-btn {\r\n      background: #3478f6;\r\n\r\n      &.disabled {\r\n        background: #CCCCCC;\r\n      }\r\n\r\n      .confirm-text {\r\n        font-size: 32rpx;\r\n        color: #ffffff;\r\n        font-weight: 600;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n/* 旋转动画 */\r\n@keyframes spin {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'E:/youngProject/agent-mini-ui/pages/create-agent/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "reactive", "ref", "computed", "uni", "updataFileFun", "generateAvatar<PERSON>pi", "getCategoryListApi", "getMyDetailApi", "onLoad", "onMounted", "updateMyAgentApi", "createAgentApi"], "mappings": ";;;;;;;;;AA4NA,UAAM,YAAYA,YAAAA,aAAc;AAGhC,UAAM,YAAYC,cAAAA,SAAS;AAAA,MACzB,cAAc,UAAU;AAAA,MACxB,WAAW;AAAA,MACX,WAAW;AAAA;AAAA,MACX,cAAc;AAAA;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,QAAQ;AAAA;AAAA,MACR,OAAO;AAAA,MACP,kBAAkB;AAAA;AAAA,MAClB,UAAU;AAAA;AAAA,MACV,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAMX,gBAAgB;AAAA,QAChB,WAAW;AAAA,QACX,cAAc;AAAA,QACd,iBAAiB;AAAA,MAClB;AAAA,MACD,kBAAkB,CAAE;AAAA,MACpB,gBAAgB;AAAA,MAChB,iBAAiB,CAAC,IAAI,IAAI,EAAE;AAAA;AAAA,IAC9B,CAAC;AAGD,UAAM,WAAWC,cAAG,IAAC,KAAK;AAC1B,UAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAG5B,UAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,UAAM,WAAWA,cAAG,IAAC,EAAE;AAGvB,UAAM,mBAAmBA,cAAG,IAAC,IAAI;AACjC,UAAM,eAAeA,cAAG,IAAC,EAAE;AAC3B,UAAM,oBAAoBA,cAAG,IAAC,KAAK;AACnC,UAAM,uBAAuBA,cAAG,IAAC,IAAI;AACrC,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAG5B,UAAM,qBAAqBC,cAAQ,SAAC,MAAM;AACxC,UAAI,CAAC,cAAc,OAAO;AACxB,eAAO,aAAa;AAAA,MACrB;AACD,aAAO,aAAa,MAAM;AAAA,QAAO,UAC/B,KAAK,aAAa,YAAa,EAAC,SAAS,cAAc,MAAM,aAAa;AAAA,MAC3E;AAAA,IACH,CAAC;AAGD,UAAM,aAAaD,cAAAA,IAAI;AAAA,MACrB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,QAAQ,OAAO,EAAG;AAAA,MAC3B,EAAE,OAAO,QAAQ,OAAO,EAAG;AAAA;AAAA,IAE7B,CAAC;AAGD,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,EAAE,OAAO,SAAS,OAAO,UAAW;AAAA,MACpC,EAAE,OAAO,YAAY,OAAO,OAAQ;AAAA,IACtC,CAAC;AACgBA,kBAAG,IAAC,SAAS;AAE9B,UAAM,kBAAkBA,cAAAA,IAAI;AAAA,MAC1B,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,MACzB,EAAE,OAAO,MAAM,OAAO,EAAG;AAAA,IAC3B,CAAC;AAED,UAAM,WAAW,MAAM;AACrBE,oBAAAA,MAAY,MAAA,OAAA,uCAAA,QAAQ;AACpBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK;AAAA,MACT,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,MAAM;AACzB,UAAI,UAAU;AAAO;AAErBA,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,OAAO,QAAQ;AACtB,gBAAM,eAAe,IAAI,cAAc,CAAC;AACxC,gBAAM,aAAa,YAAY;AAAA,QAChC;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAAA,MAAc,MAAA,SAAA,uCAAA,WAAW,GAAG;AAAA,QAC7B;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,eAAe,OAAO,aAAa;AACvC,UAAI;AACF,kBAAU,QAAQ;AAClBA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,YAAY,MAAMC,WAAa,cAAC,QAAQ;AAC9C,cAAM,SAAS,KAAK,MAAM,UAAU,IAAI;AAExC,YAAI,OAAO,SAAS,GAAG;AACrB,oBAAU,cAAc,OAAO;AAC/BD,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACP,OAAW;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,MAAM;AAAA,QACrC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,kBAAU,QAAQ;AAClBA,sBAAAA,MAAI,YAAa;AAAA,MAClB;AAAA,IACH;AAEA,UAAM,eAAe,YAAY;AAE/B,UAAI,CAAC,UAAU,UAAU,QAAQ;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,UAAU,UAAU,QAAQ;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,WAAW;AAAO;AAEtB,UAAI;AACF,mBAAW,QAAQ;AACnBA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,eAAe;AAAA,UACnB,cAAc,UAAU;AAAA,UACxB,WAAW,UAAU,UAAU,KAAM;AAAA,UACrC,WAAW,UAAU,UAAU,KAAM;AAAA,QACtC;AAED,cAAM,MAAM,MAAME,UAAiB,kBAAC,YAAY;AAEhD,YAAI,IAAI,SAAS,GAAG;AAClB,oBAAU,cAAc,IAAI,KAAK,KAAK;AACtCF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AAAA,QACP,OAAW;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,QAClC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,mBAAW,QAAQ;AACnBA,sBAAAA,MAAI,YAAa;AAAA,MAClB;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,UAAU;AACjC,gBAAU,YAAY;AAAA,IACxB;AAGA,UAAM,mBAAmB,CAAC,UAAU;AAClC,gBAAU,SAAS;AAAA,IACrB;AAEA,UAAM,mBAAmB,CAAC,UAAU;AAClC,gBAAU,YAAY,kBAAkB;AAAA,IAC1C;AAEA,UAAM,kBAAkB,YAAY;AAClC,UAAI;AACF,cAAM,MAAM,MAAMG,6BAAmB;AAAA,UACnC,cAAc,UAAU;AAAA,QAC9B,CAAK;AACD,YAAI,IAAI,SAAS,GAAG;AAClB,uBAAa,QAAQ,IAAI,QAAQ,CAAE;AAAA,QACpC;AAAA,MACF,SAAQ,OAAO;AACdH,sBAAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,aAAa,MAAM,WAAW,GAAG;AACnC,wBAAiB;AAAA,MAClB;AAGD,wBAAkB,QAAQ;AAAA,IAC5B;AAGA,UAAM,qBAAqB,MAAM;AAC/B,wBAAkB,QAAQ;AAC1B,oBAAc,QAAQ;AAAA,IAExB;AAGA,UAAM,eAAe,MAAM;AAAA,IAG3B;AAGA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AAAA,IAExB;AAEA,UAAM,qBAAqB,CAAC,aAAa;AAEvC,2BAAqB,QAAQ;AAAA,IAC/B;AAGA,UAAM,wBAAwB,MAAM;AAClC,UAAI,CAAC,qBAAqB,OAAO;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,uBAAiB,QAAQ,qBAAqB;AAC9C,gBAAU,eAAe,qBAAqB,MAAM;AACpD,yBAAoB;AAAA,IACtB;AAGA,UAAM,iBAAiB,OAAO,SAAS;AACrC,UAAI;AACFA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAED,cAAM,MAAM,MAAMI,yBAAe,EAAE,KAAI,CAAE;AACzC,YAAI,IAAI,SAAS,GAAG;AAClB,gBAAM,YAAY,IAAI;AAGtB,oBAAU,YAAY,UAAU,aAAa;AAC7C,oBAAU,YAAY,UAAU,aAAa;AAC7C,oBAAU,eAAe,UAAU,gBAAgB;AACnD,oBAAU,gBAAgB,UAAU,iBAAiB;AACrD,oBAAU,YAAY,UAAU,aAAa;AAC7C,oBAAU,cAAc,UAAU,eAAe;AACjD,oBAAU,SAAS,UAAU,UAAU;AACvC,oBAAU,QAAQ,UAAU,SAAS;AACrC,oBAAU,mBAAmB,UAAU,kBAAkB;AACzD,oBAAU,WAAW,UAAU,YAAY;AAC3C,oBAAU,cAAc,UAAU,eAAe,EAAE,cAAc,IAAI,iBAAiB,WAAW,gBAAgB,GAAI;AACrH,oBAAU,mBAAmB,UAAU,oBAAoB,CAAE;AAC7D,oBAAU,iBAAiB,UAAU,kBAAkB;AACvD,oBAAU,kBAAkB,UAAU,gBAAgB,SAAS,IAAI,UAAU,kBAAkB,CAAC,IAAI,IAAI,EAAE;AAE1G,cAAI,UAAU,UAAU;AACtB,6BAAiB,QAAQ,UAAU;AAAA,UACpC;AAGD,mBAAS,QAAQ;AAAA,QACvB,OAAW;AACL,gBAAM,IAAI,MAAM,IAAI,OAAO,WAAW;AAAA,QACvC;AAAA,MACF,SAAQ,OAAO;AACdJ,sBAAAA,MAAc,MAAA,SAAA,uCAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,QACZ,CAAK;AAED,mBAAW,MAAM;AACfA,wBAAAA,MAAI,aAAc;AAAA,QACnB,GAAE,IAAI;AAAA,MACX,UAAY;AACRA,sBAAAA,MAAI,YAAa;AAAA,MAClB;AAAA,IACH;AAGAK,kBAAM,OAAC,CAAC,YAAY;AAClB,UAAI,QAAQ,MAAM;AAChB,mBAAW,QAAQ;AACnB,uBAAe,QAAQ,IAAI;AAAA,MAC5B;AAAA,IACH,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd,sBAAiB;AAAA,IACnB,CAAC;AAGD,UAAM,eAAe,YAAY;AAC/B,UAAI,SAAS;AAAO;AAGpB,UAAI,CAAC,UAAU,cAAc;AAC3BN,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI,CAAC,UAAU,UAAU,QAAQ;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAUD,UAAI;AACF,iBAAS,QAAQ;AACjBA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO,WAAW,QAAQ,WAAW;AAAA,UACrC,MAAM;AAAA,QACZ,CAAK;AAGD,YAAI,UAAU,WAAW,KAAK,CAAC,UAAU,OAAO;AAC9CA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD,mBAAS,QAAQ;AACjBA,wBAAAA,MAAI,YAAa;AACjB;AAAA,QACD;AAGD,cAAM,oBAAoB,UAAU,gBAAgB,OAAO,OAAK,EAAE,MAAM;AAExE,cAAM,aAAa;AAAA,UACjB,WAAW,UAAU,UAAU,KAAM;AAAA,UACrC,WAAW,UAAU;AAAA,UACrB,cAAc,UAAU;AAAA,UACxB,eAAe,UAAU,cAAc,KAAM;AAAA,UAC7C,WAAW,UAAU,UAAU,KAAM;AAAA,UACrC,aAAa,UAAU;AAAA,UACvB,QAAQ,UAAU;AAAA,UAClB,OAAO,UAAU,WAAW,IAAI,WAAW,UAAU,KAAK,IAAI;AAAA,UAC9D,UAAU;AAAA;AAAA,UACV,kBAAkB,UAAU;AAAA;AAAA,UAC5B,aAAa,UAAU;AAAA,UACvB,kBAAkB,UAAU;AAAA,UAC5B,gBAAgB,UAAU,eAAe,KAAM;AAAA,UAC/C,iBAAiB;AAAA,QAClB;AAGD,YAAI,WAAW,OAAO;AACpB,qBAAW,OAAO,SAAS;AAC3BA,wBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY,UAAU;AAClC,gBAAM,MAAM,MAAMO,UAAgB,iBAAC,UAAU;AAC7C,cAAI,IAAI,SAAS,GAAG;AAClBP,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAED,uBAAW,MAAM;AACfA,4BAAAA,MAAI,aAAc;AAAA,YACnB,GAAE,IAAI;AAAA,UACf,OAAa;AACL,kBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,UAClC;AAAA,QACP,OAAW;AACL,qBAAW,eAAe,UAAU;AACpCA,wBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY,UAAU;AAClC,gBAAM,MAAM,MAAMQ,UAAc,eAAC,UAAU;AAC3C,cAAI,IAAI,SAAS,GAAG;AAClBR,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,YAChB,CAAS;AAED,uBAAW,MAAM;AACfA,4BAAAA,MAAI,aAAc;AAAA,YACnB,GAAE,IAAI;AAAA,UACf,OAAa;AACL,kBAAM,IAAI,MAAM,IAAI,OAAO,MAAM;AAAA,UAClC;AAAA,QACF;AAAA,MAEF,SAAQ,OAAO;AACdA,4BAAA,MAAA,SAAA,uCAAc,WAAW,QAAQ,UAAU,SAAS,KAAK;AACzDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,QAAQ,WAAW,QAAQ,SAAS;AAAA,UACjD,MAAM;AAAA,QACZ,CAAK;AAAA,MACL,UAAY;AACR,iBAAS,QAAQ;AACjBA,sBAAAA,MAAI,YAAa;AAAA,MAClB;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtpBA,GAAG,WAAW,eAAe;"}