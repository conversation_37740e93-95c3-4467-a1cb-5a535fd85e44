"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const api_common = require("../../api/common.js");
const stores_user = require("../../stores/user.js");
const utils_utils = require("../../utils/utils.js");
if (!Array) {
  const _easycom_ua_markdown2 = common_vendor.resolveComponent("ua-markdown");
  const _easycom_uv_badge2 = common_vendor.resolveComponent("uv-badge");
  const _easycom_z_paging2 = common_vendor.resolveComponent("z-paging");
  const _easycom_uni_popup2 = common_vendor.resolveComponent("uni-popup");
  (_easycom_ua_markdown2 + _easycom_uv_badge2 + _easycom_z_paging2 + _easycom_uni_popup2)();
}
const _easycom_ua_markdown = () => "../../components/ua-markdown/ua-markdown.js";
const _easycom_uv_badge = () => "../../uni_modules/uv-badge/components/uv-badge/uv-badge.js";
const _easycom_z_paging = () => "../../uni_modules/z-paging/components/z-paging/z-paging.js";
const _easycom_uni_popup = () => "../../uni_modules/uni-popup/components/uni-popup/uni-popup.js";
if (!Math) {
  (_easycom_ua_markdown + _easycom_uv_badge + _easycom_z_paging + _easycom_uni_popup + SubscribePopup)();
}
const SubscribePopup = () => "../../components/subscribe-popup/subscribe-popup.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const paging = common_vendor.ref(null);
    const authPopup = common_vendor.ref(null);
    const userStore = stores_user.useUserStore();
    const agentGuid = common_vendor.ref("");
    const sessionGuid = common_vendor.ref("");
    const isFirstLoad = common_vendor.ref(true);
    common_vendor.ref("");
    const dataList = common_vendor.ref([]);
    const isSending = common_vendor.ref(false);
    const ended = common_vendor.ref(false);
    const plugin = requirePlugin("WechatSI");
    const manager = plugin.getRecordRecognitionManager();
    const recorderPermission = common_vendor.ref(false);
    const agentDetail = common_vendor.reactive({
      agentName: "",
      agentAvatar: "",
      agentDesc: "",
      price: 0
    });
    const commonQuestions = common_vendor.ref([]);
    const showSubscribeModal = common_vendor.ref(false);
    const queryStatusNum = common_vendor.ref(0);
    const subscribeAgent = async () => {
      try {
        const res = await api_index.subscribeAgentApi({
          merchantGuid: userStore.merchantGuid,
          agentGuid: agentGuid.value
        });
        sessionGuid.value = res.data.sessionGuid;
      } catch (error) {
        common_vendor.index.showToast({
          title: error.msg || "订阅失败",
          icon: "none"
        });
      }
    };
    const handleClearAll = async () => {
      common_vendor.index.showModal({
        title: "确认重置",
        content: "确定要重置对话吗？",
        success: async (res) => {
          if (res.confirm) {
            let res2 = await api_index.deleteAllMessagesApi({
              merchantGuid: userStore.merchantGuid,
              sessionGuid: sessionGuid.value
            });
            if (res2.code === 0) {
              common_vendor.index.showToast({
                title: "已重置",
                icon: "success"
              });
              clearInterval(globalTimer.value);
              msgConfig.msgId = "";
              isSending.value = false;
              isFirstLoad.value = false;
              await paging.value.reload();
              addClearChat(agentDetail.agentDesc);
              isFirstSend.value = false;
            } else {
              common_vendor.index.showToast({
                title: res2.msg,
                icon: "none"
              });
            }
          }
        }
      });
    };
    const handleCollect = async (item) => {
      let guid = item.guid;
      let title = "收藏成功";
      try {
        if (item.isCollected) {
          await api_index.cancelCollectMessageApi({
            merchantGuid: userStore.merchantGuid,
            messageGuid: guid
          });
          title = "取消收藏成功";
          item.isCollected = false;
        } else {
          await api_index.collectMessageApi({
            merchantGuid: userStore.merchantGuid,
            messageGuid: guid
          });
          item.isCollected = true;
        }
        common_vendor.index.showToast({
          title,
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: error.msg,
          icon: "none"
        });
      }
    };
    const handleCopy = (content) => {
      common_vendor.index.setClipboardData({
        data: content,
        success() {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "none"
          });
        }
      });
    };
    const onGoAi = () => {
      common_vendor.index.showToast({
        title: "近期上线，敬请关注",
        icon: "none"
      });
    };
    const getAgentDetail = async () => {
      let res = await api_index.agentDetailApi({
        merchantGuid: userStore.merchantGuid,
        agentGuid: agentGuid.value
      });
      agentDetail.agentName = res.data.agentName;
      agentDetail.agentAvatar = res.data.agentAvatar;
      commonQuestions.value = res.data.commonQuestions;
      agentDetail.agentDesc = res.data.agentDesc;
      agentDetail.price = res.data.priceYuan;
    };
    let historyMsgLoading = common_vendor.ref(true);
    const queryList = async (page, pageSize) => {
      let req = {
        merchantGuid: userStore.merchantGuid,
        sessionGuid: sessionGuid.value,
        startId: 0,
        //开始的聊天记录ID，当用户清除历史消息时使用
        page,
        //当前页数
        isAll: 0
      };
      try {
        let res = await api_index.getMessageHistoryApi(req);
        historyMsgLoading.value = false;
        common_vendor.index.setNavigationBarTitle({
          title: res.data.sessionInfo.agentInfo.agentName
        });
        let msg = [];
        if (res.data.list.length > 0) {
          msgConfig.msgId = res.data.list[0].msgId;
          res.data.list.forEach((item) => {
            if (item.chatRole === "assistant") {
              let type = item.contentType;
              if (item.contentType === "img") {
                type = "image";
              }
              if (item.contentType === "url") {
                type = "link";
              }
              if (item.imageList === null) {
                item.imageList = [];
              }
              msg.push({
                role: "assistant",
                content: item.chatContent,
                isSuccessData: true,
                isNewMsg: false,
                type,
                guid: item.guid,
                aiReplyTitle: "",
                msgId: item.lastMsgId,
                isCollected: item.isCollected,
                imageList: item.imageList
              });
            } else if (item.chatRole === "user") {
              msg.push({
                role: "user",
                content: item.chatContent,
                msgId: item.msgId
              });
            }
          });
        }
        if (msg.length > 0 && msg[msg.length - 1].role === "assistant") {
          msg[msg.length - 1].isFirst = true;
        } else {
          if (isFirstLoad.value) {
            common_vendor.index.__f__("log", "at pages/msg/index.vue:421", "这里什么情况");
            msg.push({
              role: "assistant",
              content: agentDetail.agentDesc,
              isSuccessData: false,
              isLoading: false,
              type: "text",
              aiReplyTitle: "",
              isFirst: true,
              msgId: "",
              guid: "first_none_guid",
              imageList: []
            });
            isFirstLoad.value = false;
          }
        }
        await paging.value.complete(msg);
      } catch (error) {
        historyMsgLoading.value = false;
        paging.value.complete(false);
      }
    };
    const onOftenClick = utils_utils.throttle((item) => {
      msgContent.value = item;
      onSend();
    }, 1e3);
    const addAiChat = (answer, type = "text", isFirst = false) => {
      let parame = {
        content: "",
        title: "",
        imageList: []
        // ...answer
      };
      let reMsg = {
        role: "assistant",
        content: answer,
        isSuccessData: false,
        isLoading: true,
        type,
        aiReplyTitle: parame.title,
        isFirst,
        isCollected: false,
        msgId: "",
        guid: `ai_${Date.now()}_${Math.random()}`,
        // 添加临时唯一标识符，后续会被服务器返回的guid替换
        imageList: parame.imageList
      };
      paging.value.addChatRecordData(reMsg);
    };
    const addClearChat = (answer) => {
      let reMsg = {
        role: "assistant",
        content: answer,
        isSuccessData: false,
        isLoading: false,
        type: "text",
        aiReplyTitle: "",
        isFirst: true,
        isCollected: false,
        msgId: "",
        guid: "first_none_guid",
        imageList: []
      };
      paging.value.addChatRecordData(reMsg);
    };
    const addUserChat = (content) => {
      let reMsg = {
        role: "user",
        content,
        msgId: "",
        isLoading: true,
        type: "text",
        imgs: [],
        guid: `user_${Date.now()}_${Math.random()}`
        // 添加唯一标识符
      };
      if (msgUserReqParame.contentType === "image") {
        reMsg.type = "image";
        reMsg.imgs = [...msgUserReqParame.imgs];
      }
      paging.value.addChatRecordData(reMsg);
    };
    const onGotoMini = (appid) => {
      common_vendor.index.navigateToMiniProgram({
        appId: appid,
        success(res) {
          common_vendor.index.__f__("log", "at pages/msg/index.vue:513", res, "success");
        },
        fail(res) {
          common_vendor.index.__f__("log", "at pages/msg/index.vue:516", res, "fail");
        }
      });
    };
    const msgContent = common_vendor.ref("");
    const msgConfig = common_vendor.reactive({
      isNewGlobalMsg: false,
      nowAiMsgIndex: 0,
      msgId: "",
      nowChatLunciGuid: ""
    });
    let isMsgInput = common_vendor.ref(true);
    const onMsgFocus = () => {
      isMsgInput.value = false;
    };
    const onMsgBlur = () => {
      isMsgInput.value = true;
    };
    const globalTimer = common_vendor.ref("");
    common_vendor.ref(30);
    let msgUserReqParame = common_vendor.reactive({
      merchantGuid: userStore.merchantGuid,
      sessionGuid: "",
      role: "user",
      content: "",
      lastMsgId: "",
      contentType: "text",
      sceneValue: "",
      chatLunciGuid: "",
      imgs: []
    });
    common_vendor.reactive({
      merchantGuid: userStore.merchantGuid,
      role: "assistant",
      sessionGuid: "",
      content: "",
      lastMsgId: "",
      contentType: "",
      sceneValue: "",
      chatLunciGuid: "",
      imgs: []
    });
    const onViceoSend = () => {
      onSend();
    };
    let isFirstSend = common_vendor.ref(false);
    const onSend = async () => {
      if (msgContent.value.trim().length === 0) {
        common_vendor.wx$1.showToast({
          title: "请输入问题 ...",
          icon: "none",
          duration: 1e3
        });
        return;
      }
      if (isSending.value) {
        common_vendor.wx$1.showToast({
          title: "回答中...",
          icon: "none",
          duration: 1e3
        });
        return;
      }
      isSending.value = true;
      if (globalTimer.value) {
        clearInterval(globalTimer.value);
        globalTimer.value = "";
      }
      addUserChat(msgContent.value);
      addAiChat(" ");
      msgConfig.nowAiMsgIndex = 0;
      msgUserReqParame.content = msgContent.value;
      msgUserReqParame.lastMsgId = msgConfig.msgId;
      msgUserReqParame.sessionGuid = sessionGuid.value;
    };
    const onMsgLink = (link) => {
      common_vendor.index.navigateTo({
        url: "/pages/webview/webview",
        success: (res) => {
          res.eventChannel.emit("urlEvent", decodeURIComponent(link));
        },
        fail(res) {
          common_vendor.index.__f__("log", "at pages/msg/index.vue:765", res);
        }
      });
    };
    const authCallback = (res) => {
      authPopup.value.close();
    };
    const onLookImg = (urls, isMore, index) => {
      let img = [];
      if (isMore) {
        img = urls;
      } else {
        img[0] = urls;
      }
      common_vendor.index.previewImage({
        current: index,
        urls: img,
        longPressActions: {
          itemList: ["发送给朋友", "保存图片", "收藏"],
          success: function(data) {
          },
          fail: function(err) {
            common_vendor.index.__f__("log", "at pages/msg/index.vue:801", err.errMsg);
          }
        }
      });
    };
    const recordConfig = common_vendor.reactive({
      recorderMode: 1,
      // 1显示 按住说话 2显示 说话中
      isRecorderMode: false,
      //是否显示 语音输入按钮
      voiceText: "按住说话",
      voiceTitle: "松手结束录音",
      // delShow: false,
      sendLock: true,
      //发送锁，当为true时上锁，false时解锁发送
      isCloseSend: false,
      //是否取消发送
      // time: 0, //录音时长
      // tempFilePath: '', //音频路径
      duration: 6e4,
      //录音最大值ms 60000/1分钟
      startPoint: null,
      //记录长按录音开始点信息,用于后面计算滑动距离。
      isAnalyzeDisabled: false,
      //是否在文本解析过程中
      recording: false
      // 正在录音
    });
    const onLongTap = (e) => {
      if (!recorderPermission.value) {
        common_vendor.wx$1.showToast({
          title: "当前版本不支持,或您未打开麦克风使用权限",
          icon: "none",
          duration: 1e3
        });
        return;
      }
      if (recordConfig.isAnalyzeDisabled) {
        common_vendor.wx$1.showToast({
          title: "正在识别语音中...",
          icon: "none",
          duration: 1e3
        });
        return;
      }
      if (msgConfig.isNewGlobalMsg) {
        common_vendor.wx$1.showToast({
          title: "正在回答中...",
          icon: "none",
          duration: 1e3
        });
        return;
      }
      recordConfig.startPoint = e.touches[0];
      recordConfig.recorderMode = 2;
      recordConfig.voiceText = "说话中...";
      recordConfig.recording = true;
      manager.start({
        duration: 6e4,
        lang: "zh_CN"
      });
      recordConfig.sendLock = false;
    };
    const onTouchend = () => {
      if (recordConfig.isCloseSend) {
        recordInit();
        return;
      }
      if (recordConfig.isAnalyzeDisabled) {
        return;
      }
      if (msgConfig.isNewGlobalMsg) {
        return;
      }
      recordInit();
      if (!recordConfig.recording) {
        return;
      }
      manager.stop();
      recordConfig.isAnalyzeDisabled = true;
      if (!recordConfig.sendLock) {
        common_vendor.index.showLoading({
          title: "识别中",
          mask: true
        });
      }
    };
    const onTouchMove = (e) => {
      if (!e || !e.touches || e.touches.length === 0 || !recordConfig.startPoint)
        return;
      let moveLenght = e.touches[e.touches.length - 1].clientY - recordConfig.startPoint.clientY;
      if (Math.abs(moveLenght) > 30) {
        recordConfig.voiceTitle = "松开手指,取消发送";
        recordConfig.voiceText = "松开手指,取消发送";
        recordConfig.sendLock = true;
        recordConfig.isCloseSend = true;
      } else {
        recordConfig.voiceTitle = "松手结束录音";
        recordConfig.voiceText = "松手结束录音";
        recordConfig.sendLock = false;
        recordConfig.isCloseSend = false;
      }
    };
    const recordInit = () => {
      recordConfig.recorderMode = 1;
      recordConfig.voiceText = "按住说话";
      recordConfig.voiceTitle = "松手结束录音";
      recordConfig.isCloseSend = false;
    };
    const initRecord = () => {
      manager.onRecognize = (res) => {
        if (recordConfig.sendLock) {
          return;
        } else {
          msgContent.value = res.result;
          onViceoSend();
        }
      };
      manager.onStop = (res) => {
        common_vendor.index.__f__("log", "at pages/msg/index.vue:933", "onStoponStop", res);
        recordConfig.isAnalyzeDisabled = false;
        recordConfig.recording = false;
        if (recordConfig.sendLock) {
          return;
        }
        common_vendor.index.hideLoading();
        let text = res.result;
        if (text == "") {
          return;
        }
        msgContent.value = text;
        onViceoSend();
      };
      manager.onError = (res) => {
        common_vendor.index.hideLoading();
        recordConfig.isAnalyzeDisabled = false;
        recordConfig.recording = false;
        let code = res.retcode;
        switch (code) {
          case -30003:
            common_vendor.wx$1.showToast({
              title: "未检测到语音",
              icon: "none",
              duration: 1e3
            });
            break;
          default:
            common_vendor.wx$1.showToast({
              title: "未检测到语音~",
              icon: "none",
              duration: 1e3
            });
            break;
        }
      };
    };
    const getRecordPer = () => {
      common_vendor.wx$1.getSetting({
        success(res) {
          if (!res.authSetting["scope.record"]) {
            common_vendor.wx$1.authorize({
              scope: "scope.record",
              success() {
                recorderPermission.value = true;
              },
              fail() {
                recorderPermission.value = false;
              }
            });
          } else {
            recorderPermission.value = true;
          }
        }
      });
    };
    const showSubscribePopup = () => {
      showSubscribeModal.value = true;
    };
    const closeSubscribePopup = () => {
      showSubscribeModal.value = false;
    };
    const handleSubscribeConfirm = async () => {
      let payInfo = await api_index.createPurchaseOrderApi({
        merchantGuid: userStore.merchantGuid,
        agentGuid: agentGuid.value,
        payEnv: "xcx"
      });
      api_common.miniPay(payInfo.data.payInfo).then(
        async (res) => {
          queryPayChatStauts(payInfo.data.orderNo, queryStatusNum);
        },
        (res) => {
          common_vendor.index.showToast({
            title: res.msg
          });
        }
      );
    };
    const queryPayChatStauts = async (orderNo, number) => {
      number++;
      try {
        let orderInfo = await api_index.queryPurchaseOrderApi({
          orderNo
        });
        if (orderInfo.data.isPaid) {
          common_vendor.index.showToast({
            title: "支付成功"
          });
          ended.value = false;
          showSubscribeModal.value = false;
        } else {
          if (number > 12) {
            common_vendor.index.showToast({
              title: "支付失败"
            });
          } else {
            queryPayChatStauts(orderNo, number);
          }
        }
      } catch (e) {
        common_vendor.index.showToast({
          title: e.msg ? e.msg : "支付失败"
        });
      }
    };
    const uploading = common_vendor.ref(false);
    const chooseimg = () => {
      if (uploading.value)
        return;
      common_vendor.index.chooseImage({
        count: 9,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0];
          await uploadImg(tempFilePath);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/msg/index.vue:1065", "选择图片失败:", err);
        }
      });
    };
    const uploadImg = async (filePath) => {
      try {
        uploading.value = true;
        common_vendor.index.showLoading({
          title: "上传中...",
          mask: true
        });
        const uploadRes = await api_common.updataFileFun(filePath);
        const result = JSON.parse(uploadRes.data);
        if (result.code === 0) {
          msgUserReqParame.imgs[0] = result.data;
          msgUserReqParame.contentType = "image";
          common_vendor.index.showToast({
            title: "上传成功",
            icon: "success"
          });
        } else {
          throw new Error(result.msg || "上传失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/msg/index.vue:1092", "上传失败:", error);
        common_vendor.index.showToast({
          title: "上传失败",
          icon: "none"
        });
      } finally {
        uploading.value = false;
        common_vendor.index.hideLoading();
      }
    };
    common_vendor.onLoad(async (params) => {
      getRecordPer();
      initRecord();
      if (params.sessionGuid) {
        agentGuid.value = params.sessionGuid;
        await subscribeAgent();
        await getAgentDetail();
      }
      if (userStore.userToken) {
        common_vendor.nextTick$1(() => {
          if (paging.value) {
            paging.value.reload();
          }
        });
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(dataList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.role === "assistant"
          }, item.role === "assistant" ? common_vendor.e({
            b: item.aiReplyTitle && item.aiReplyTitle.length > 0
          }, item.aiReplyTitle && item.aiReplyTitle.length > 0 ? {
            c: common_vendor.t(item.aiReplyTitle)
          } : {}, {
            d: item.type === "richText"
          }, item.type === "richText" ? common_vendor.e({
            e: item.content,
            f: item.imageList && item.imageList.length > 0
          }, item.imageList && item.imageList.length > 0 ? {
            g: common_vendor.f(item.imageList, (path, index2, i1) => {
              return {
                a: common_vendor.o(($event) => onLookImg(item.imageList, true, index2), index2),
                b: path,
                c: index2
              };
            }),
            h: item.imageList.length < 3 ? 1 : ""
          } : {}) : {}, {
            i: item.type === "text"
          }, item.type === "text" ? common_vendor.e({
            j: "6ddef3fc-1-" + i0 + ",6ddef3fc-0",
            k: common_vendor.p({
              source: item.content
            }),
            l: item.isLoading
          }, item.isLoading ? {} : {}, {
            m: item.isFirst && !common_vendor.unref(isFirstSend)
          }, item.isFirst && !common_vendor.unref(isFirstSend) ? {
            n: common_vendor.f(commonQuestions.value, (item2, index2, i1) => {
              return {
                a: common_vendor.t(item2),
                b: common_vendor.o(($event) => common_vendor.unref(onOftenClick)(item2), index2),
                c: index2
              };
            })
          } : {}) : {}, {
            o: item.type === "image"
          }, item.type === "image" ? {
            p: common_vendor.o(($event) => onLookImg(item.content, false, 0), item.guid || `temp_${index}_${item.role}_${Date.now()}`),
            q: item.content
          } : {}, {
            r: item.type === "link"
          }, item.type === "link" ? {
            s: common_vendor.t(item.content),
            t: common_vendor.o(($event) => onMsgLink(item.content), item.guid || `temp_${index}_${item.role}_${Date.now()}`)
          } : {}, {
            v: item.type === "video"
          }, item.type === "video" ? {
            w: item.content
          } : {}, {
            x: item.type === "mini"
          }, item.type === "mini" ? {
            y: common_vendor.o(($event) => onGotoMini(item.content), item.guid || `temp_${index}_${item.role}_${Date.now()}`)
          } : {}, {
            z: !item.isFirst
          }, !item.isFirst ? common_vendor.e({
            A: item.guid
          }, item.guid ? {
            B: common_vendor.o(($event) => handleCollect(item), item.guid || `temp_${index}_${item.role}_${Date.now()}`),
            C: item.isCollected ? common_vendor.unref(common_assets.scOnIcon) : common_vendor.unref(common_assets.scIcon)
          } : {}, {
            D: common_vendor.o(($event) => handleCopy(item.content), item.guid || `temp_${index}_${item.role}_${Date.now()}`),
            E: common_assets._imports_0$8,
            F: index === dataList.value.findIndex((msg) => msg.role === "assistant")
          }, index === dataList.value.findIndex((msg) => msg.role === "assistant") ? {
            G: common_vendor.o(onGoAi, item.guid || `temp_${index}_${item.role}_${Date.now()}`)
          } : {}) : {}, {
            H: item.aiReplyTitle.length === 0 ? 1 : ""
          }) : {}, {
            I: item.role === "user"
          }, item.role === "user" ? common_vendor.e({
            J: common_vendor.t(item.content),
            K: item.imgs && item.imgs.length > 0
          }, item.imgs && item.imgs.length > 0 ? {
            L: common_vendor.f(item.imgs, (path, index2, i1) => {
              return {
                a: common_vendor.o(($event) => onLookImg(item.imgs, true, index2), index2),
                b: path,
                c: index2
              };
            }),
            M: item.imgs.length < 3 ? 1 : ""
          } : {}) : {}, {
            N: item.guid || `temp_${index}_${item.role}_${Date.now()}`
          });
        }),
        b: agentDetail.agentAvatar,
        c: common_vendor.unref(isMsgInput) && !ended.value
      }, common_vendor.unref(isMsgInput) && !ended.value ? {
        d: common_vendor.o(handleClearAll)
      } : {}, {
        e: ended.value
      }, ended.value ? {
        f: common_vendor.o(showSubscribePopup)
      } : common_vendor.e({
        g: !recordConfig.isRecorderMode
      }, !recordConfig.isRecorderMode ? common_vendor.e({
        h: common_vendor.unref(historyMsgLoading),
        i: common_vendor.o(onMsgFocus),
        j: common_vendor.o(onMsgBlur),
        k: common_vendor.o(onSend),
        l: msgContent.value,
        m: common_vendor.o(($event) => msgContent.value = $event.detail.value),
        n: common_vendor.unref(isMsgInput)
      }, common_vendor.unref(isMsgInput) ? {
        o: common_assets._imports_1$5,
        p: common_vendor.o(onLongTap),
        q: common_vendor.o(onTouchend),
        r: common_vendor.o(onTouchMove)
      } : {
        s: common_assets._imports_2$4,
        t: common_vendor.o(onSend)
      }, {
        v: common_assets._imports_3$2,
        w: common_vendor.o(chooseimg),
        x: common_vendor.unref(msgUserReqParame).imgs.length > 0
      }, common_vendor.unref(msgUserReqParame).imgs.length > 0 ? {
        y: common_vendor.p({
          value: 1,
          type: "error",
          absolute: true,
          offset: [0, 0]
        })
      } : {}) : {}, {
        z: recordConfig.recorderMode == 2
      }, recordConfig.recorderMode == 2 ? {
        A: common_vendor.t(recordConfig.voiceTitle),
        B: recordConfig.isCloseSend ? 1 : ""
      } : {}), {
        C: common_vendor.sr(paging, "6ddef3fc-0", {
          "k": "paging"
        }),
        D: common_vendor.o(queryList),
        E: common_vendor.o(($event) => dataList.value = $event),
        F: common_vendor.p({
          ["auto-hide-keyboard-when-chat"]: false,
          ["hide-empty-view"]: true,
          ["use-chat-record-mode"]: true,
          auto: false,
          ["auto-clean-list-when-reload"]: false,
          ["show-chat-loading-when-reload"]: true,
          modelValue: dataList.value
        }),
        G: common_vendor.o(authCallback),
        H: common_vendor.sr(authPopup, "6ddef3fc-3", {
          "k": "authPopup"
        }),
        I: common_vendor.o(closeSubscribePopup),
        J: common_vendor.o(handleSubscribeConfirm),
        K: common_vendor.p({
          show: showSubscribeModal.value,
          agentInfo: agentDetail
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6ddef3fc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/msg/index.js.map
