"use strict";const e=require("../../common/vendor.js"),o=require("../../common/assets.js"),t=require("../../api/index.js"),s=require("../../stores/user.js"),i={__name:"video-complete",setup(i){const a=s.useUserStore(),n=e.ref(""),r=e.ref(""),d=e.ref(""),c=async()=>{if(n.value){e.index.showLoading({title:"正在保存..."});try{const o=await new Promise(((o,t)=>{e.index.downloadFile({url:n.value,success:e=>{200===e.statusCode?o(e.tempFilePath):t(new Error("下载失败"))},fail:e=>{t(e)}})}));await new Promise(((t,s)=>{e.index.saveVideoToPhotosAlbum({filePath:o,success:()=>{t()},fail:o=>{o.errMsg.includes("auth")&&e.index.showModal({title:"提示",content:"需要您授权访问相册才能保存视频，请在设置中开启相册权限",showCancel:!1}),s(o)}})})),e.index.hideLoading(),e.index.showToast({title:"保存成功",icon:"success",duration:2e3})}catch(o){e.index.hideLoading(),console.error("保存视频失败:",o),e.index.showToast({title:"保存失败，请重试",icon:"none",duration:2e3})}}else e.index.showToast({title:"视频还未加载完成",icon:"none"})},l=()=>({title:"我制作了一个精彩的视频，快来看看吧！",path:`/pages/my/video-complete?orderNo=${d.value}`,imageUrl:r.value||""});return e.onLoad((o=>{o.orderNo?(d.value=o.orderNo,(async()=>{try{const o=await t.getVideoDetailApi({merchantGuid:a.merchantGuid,orderNo:d.value});if(0===o.code){const{videoUrl:e,previewUrl:t}=o.data;e&&(n.value=e),t&&(r.value=t)}else console.error("获取视频详情失败:",o.msg),e.index.showToast({title:"获取视频失败",icon:"none"})}catch(o){console.error("获取视频详情失败:",o),e.index.showToast({title:"获取视频失败",icon:"none"})}})()):n.value="https://vd3.bdstatic.com/mda-ka0x6301f525mw5e/mda-ka0x6301f525mw5e.mp4?playlist=%5B%22hd%22%2C%22sc%22%5D"})),e.onMounted((()=>{})),(t,s)=>({a:n.value,b:r.value,c:o._imports_0$7,d:e.o(c),e:o._imports_1$4,f:e.o(l)})}},a=e._export_sfc(i,[["__scopeId","data-v-01ec628c"]]);i.__runtimeHooks=2,wx.createPage(a);
