<template>
  <view class="container">
    <!-- :style="{backgroundImage: 'url(https://api.zhimoai.com/storage/topic/20240830/22c0fd242cddcc1d0f7ccbbf8f4fcff1.png)',}" -->
    <z-paging ref="paging" v-model="dataList" @query="queryList" :auto-hide-keyboard-when-chat="false"
      :hide-empty-view="true" :use-chat-record-mode="true" :auto="false" :auto-clean-list-when-reload="false"
      :show-chat-loading-when-reload="true">
      <view class="chat-main">
        <view class="chat-ls" v-for="(item, index) in dataList"
          :key="item.guid || `temp_${index}_${item.role}_${Date.now()}`">
          <view style="transform: scaleY(-1)">
            <view class="msg-m msg-left" v-if="item.role === 'assistant'">
              <!-- <view class="logo-box gpt-box">
                <image class="user-img" :src="agentDetail.agentAvatar"></image>
              </view> -->
              <!-- 文字 -->
              <view class="msg-text" :class="{ hasReply: item.aiReplyTitle.length === 0 }">
                <view class="ai-reply-msg" v-if="item.aiReplyTitle && item.aiReplyTitle.length > 0">{{ item.aiReplyTitle
                }}</view>
                <view class="rich-text-box" v-if="item.type === 'richText'">
                  <rich-text :user-select="true" :nodes="item.content"></rich-text>
                  <view class="img-list-box" :class="{ noWrap: item.imageList.length < 3 }"
                    v-if="item.imageList && item.imageList.length > 0">
                    <image class="img" mode="widthFix" @click="onLookImg(item.imageList, true, index)"
                      v-for="(path, index) in item.imageList" :src="path" :key="index">
                    </image>
                  </view>
                </view>
                <view class="rich-text-box" v-if="item.type === 'text'">
                  <!-- <text :user-select="true" class="t">{{ item.content }}</text> -->
                  <!-- <rich-text :user-select="true" :nodes="item.content"></rich-text> -->
                  <ua-markdown :source="item.content" />
                  <view class="loading" v-if="item.isLoading">
                    <view class="loading-dot"></view>
                    <view class="loading-dot"></view>
                    <view class="loading-dot"></view>
                  </view>
                  <view class="often-questions" v-if="item.isFirst && !isFirstSend">
                    <view class="item" @click="onOftenClick(item)" v-for="(item, index) in commonQuestions"
                      :key="index">
                      {{ item }}
                    </view>
                  </view>
                </view>
                <view class="img-box" v-if="item.type === 'image'">
                  <image @click="onLookImg(item.content, false, 0)" :show-menu-by-longpress="true" class="img"
                    mode="widthFix" :src="item.content">
                  </image>
                </view>
                <view @click="onMsgLink(item.content)" class="text-box" v-if="item.type === 'link'">
                  <text class="link" :user-select="true">{{ item.content }}</text>
                </view>
                <view class="video-box" v-if="item.type === 'video'">
                  <video class="video-dom" id="myVideo" :src="item.content" controls></video>
                </view>
                <view class="mini-box" v-if="item.type === 'mini'">
                  <view class="btn" @click="onGotoMini(item.content)">点击前往小程序</view>
                </view>
                <view class="other-box" v-if="!item.isFirst">
                  <image @click="handleCollect(item)" v-if="item.guid" class="icon" mode="widthFix"
                    :src="item.isCollected ? scOnIcon : scIcon">
                  </image>
                  <!-- <image @click="handleDelete(item.guid)" class="icon" mode="widthFix"
                    src="@/static/msg/<EMAIL>">
                  </image> -->
                  <image @click="handleCopy(item.content)" class="icon" mode="widthFix"
                    src="@/static/msg/copy-icon.png">
                  </image>
                  <view class="text-btn" @click="onGoAi"
                    v-if="index === dataList.findIndex(msg => msg.role === 'assistant')">生成视频</view>
                </view>
              </view>
            </view>
            <view class="msg-m msg-right" v-if="item.role === 'user'">
              <!-- <view class="logo-box">
                <image class="user-img" :src="headImgUrl"></image>
              </view> -->
              <view class="msg-text">
                <text :user-select="true">{{ item.content }}</text>
                <view class="img-list-box" :class="{ noWrap: item.imgs.length < 3 }"
                  v-if="item.imgs && item.imgs.length > 0">
                  <image class="img" mode="widthFix" @click="onLookImg(item.imgs, true, index)"
                    v-for="(path, index) in item.imgs" :src="path" :key="index">
                  </image>
                </view>
              </view>
              <!-- <view class="img-box" v-if="item.type === 'image'">
                <image @click="onLookImg(item.imsg, false, 0)" :show-menu-by-longpress="true" class="img"
                  mode="widthFix" :src="item.imsg">
                </image>
              </view> -->

            </view>
          </view>
        </view>
        <!-- <view class="on-load-more-box" style="transform: scaleY(-1)" @click="onGetHistory" v-if="isFirstLoad">点击加载更多
        </view> -->
        <view class="agent-logo" style="transform: scaleY(-1)">
          <image class="logo" :src="agentDetail.agentAvatar"></image>
        </view>
      </view>
      <template #bottom>
        <view class="clear-msg" v-if="isMsgInput && !ended">
          <text class="txt" @click="handleClearAll">重置对话</text>
        </view>
        <view class="sy-end-box" v-if="ended">
          <view>试用已结束，请订阅后继续使用</view>
          <view class="end-btn" @click="showSubscribePopup">立即订阅</view>
        </view>
        <view class="submit-box" v-else>
          <!-- v-if="isMsgInput" -->
          <!-- <view class="send-record-box" @touchstart="onLongTap" @touchend="onTouchend" @touchmove="onTouchMove"
            :class="{ 'longPress': recordConfig.recorderMode == 2, 'is-close-send': recordConfig.isCloseSend }">
            <image class="icon" src="@/static/<EMAIL>" @click="onViceoSend"></image> {{ recordConfig.voiceText
            }}
          </view> -->
          <!-- :class="{ mt: isMsgInput }" mt -->
          <view class="sub-input-box ">
            <view class="input-box" v-if="!recordConfig.isRecorderMode">
              <view class="textarea-box">
                <textarea class="textarea-input" :disabled="historyMsgLoading" :fixed="true" @focus="onMsgFocus"
                  @blur="onMsgBlur" v-model="msgContent" placeholder="也可以打字哦，点击输入吧" confirmType="send" @confirm="onSend"
                  :adjust-position="false" maxlength="-1" :show-confirm-bar="false" auto-height></textarea>
              </view>
              <view class="vicoe-box">
                <view class="send" v-if="isMsgInput">
                  <image class="icon" src="@/static/<EMAIL>" @touchstart="onLongTap" @touchend="onTouchend"
                    @touchmove="onTouchMove"></image>
                </view>
                <view class="send" v-else>
                  <image class="icon" src="@/static/<EMAIL>" @click="onSend"></image>
                </view>
                <view class="upload">
                  <image class="icon" src="@/static/<EMAIL>" @click="chooseimg"></image>
                  <uv-badge v-if="msgUserReqParame.imgs.length > 0" :value="1" type="error" absolute
                    :offset="[0, 0]"></uv-badge>
                  <!-- <view class="b"></view> -->
                </view>
              </view>
            </view>
            <template v-if="recordConfig.recorderMode == 2">
              <view class="spinner-title">{{ recordConfig.voiceTitle }}</view>
              <view class="recode-loading-box" :class="{ 'is-close-send': recordConfig.isCloseSend }">
                <view class="spinner">
                  <view class="sub-spinner spinner-part-0"></view>
                  <view class="sub-spinner spinner-part-1"></view>
                  <view class="sub-spinner spinner-part-2"></view>
                  <view class="sub-spinner spinner-part-3"></view>
                  <view class="sub-spinner spinner-part-0"></view>
                  <view class="sub-spinner spinner-part-1"></view>
                  <view class="sub-spinner spinner-part-2"></view>
                  <view class="sub-spinner spinner-part-3"></view>
                </view>
              </view>
            </template>
          </view>
          <!-- <view class="recode-box record-layer" :class="{ 'record-layer': recordConfig.recorderMode == 2 }">
            <view class="recode-loading-box" :class="{ 'is-close-send': recordConfig.isCloseSend }">
              <view class="spinner">
                <view class="sub-spinner spinner-part-0"></view>
                <view class="sub-spinner spinner-part-1"></view>
                <view class="sub-spinner spinner-part-2"></view>
                <view class="sub-spinner spinner-part-3"></view>
                <view class="sub-spinner spinner-part-0"></view>
                <view class="sub-spinner spinner-part-1"></view>
                <view class="sub-spinner spinner-part-2"></view>
                <view class="sub-spinner spinner-part-3"></view>
              </view>
              <view class="spinner-title">{{ recordConfig.voiceTitle }}</view>
            </view>
          </view> -->
        </view>

      </template>
    </z-paging>
    <uni-popup ref="authPopup">
      <view class="auth-pop-box">
        <view class="title">麦克风未授权，请授权使用麦克风。</view>
        <button class="btn" open-type="openSetting" @click="authCallback">打开设置页</button>
      </view>
    </uni-popup>
    <!-- 订阅弹窗 -->
    <subscribe-popup :show="showSubscribeModal" :agentInfo="agentDetail" @close="closeSubscribePopup"
      @subscribe="handleSubscribeConfirm" />
  </view>
</template>

<script setup>
import {
  getMessageHistoryApi,
  saveMsgApi,
  subscribeAgentApi,
  deleteAllMessagesApi,
  collectMessageApi,
  cancelCollectMessageApi,
  agentDetailApi,
  createPurchaseOrderApi,
  queryPurchaseOrderApi
} from '@/api';
import { updataFileFun } from '@/api/common.js'
import {
  ref,
  reactive,
  nextTick,
} from 'vue';
import {
  onLoad,
} from '@dcloudio/uni-app';
import {
  TextDecoder
} from "text-encoding/lib/encoding";
import {
  useUserStore
} from '@/stores/user.js';
import {
  miniPay
} from '@/api/common.js'
import {
  throttle
} from '@/utils/utils.js'
import SubscribePopup from '@/components/subscribe-popup/subscribe-popup.vue';
import scIcon from '@/static/msg/<EMAIL>';
import scOnIcon from '@/static/msg/sc-on-icon.png';
import base from '@/config/config.js';
const paging = ref(null)
const authPopup = ref(null);
const userStore = useUserStore();
const agentGuid = ref('');
const sessionGuid = ref('');
const isFirstLoad = ref(true)
const conversation_id = ref('')
const dataList = ref([]);
const isSending = ref(false)
//试用是否结束
const ended = ref(false)
// #ifdef MP-WEIXIN
// const app = getApp();
// const wxRecorder = app.globalData.recorderManager;
const plugin = requirePlugin("WechatSI")
// 获取**全局唯一**的语音识别管理器**recordRecoManager**
const manager = plugin.getRecordRecognitionManager()
const recorderPermission = ref(false)
// #endif
const agentDetail = reactive({
  agentName: '',
  agentAvatar: '',
  agentDesc: '',
  price: 0,
})
const commonQuestions = ref([])
const showSubscribeModal = ref(false) // 控制订阅弹窗显示
const queryStatusNum = ref(0)

const subscribeAgent = async () => {
  try {
    const res = await subscribeAgentApi({
      merchantGuid: userStore.merchantGuid,
      agentGuid: agentGuid.value
    })
    sessionGuid.value = res.data.sessionGuid;
  } catch (error) {
    uni.showToast({
      title: error.msg || '订阅失败',
      icon: 'none'
    })
  }

}
//清空历史消息
const handleClearAll = async () => {
  uni.showModal({
    title: '确认重置',
    content: '确定要重置对话吗？',
    success: async (res) => {
      if (res.confirm) {
        let res = await deleteAllMessagesApi({
          merchantGuid: userStore.merchantGuid,
          sessionGuid: sessionGuid.value
        })
        if (res.code === 0) {
          uni.showToast({
            title: '已重置',
            icon: 'success'
          })
          //清除轮训加载文字
          clearInterval(globalTimer.value)
          msgConfig.msgId = '';
          isSending.value = false;
          isFirstLoad.value = false;
          await paging.value.reload()
          addClearChat(agentDetail.agentDesc)
          isFirstSend.value = false;
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }

      }
    }
  })
}
const handleCollect = async (item) => {
  let guid = item.guid;
  let title = '收藏成功';
  try {
    if (item.isCollected) {
      await cancelCollectMessageApi({
        merchantGuid: userStore.merchantGuid,
        messageGuid: guid
      })
      title = '取消收藏成功';
      item.isCollected = false
    } else {
      await collectMessageApi({
        merchantGuid: userStore.merchantGuid,
        messageGuid: guid
      })
      item.isCollected = true
    }
    uni.showToast({
      title: title,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: error.msg,
      icon: 'none'
    })
  }

}
const handleCopy = (content) => {
  uni.setClipboardData({
    data: content,
    success() {
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      })
    }
  })
}
const onGoAi = () => {
  uni.showToast({
    title: '近期上线，敬请关注',
    icon: 'none'
  })
}
//获取智能体详情

const getAgentDetail = async () => {
  let res = await agentDetailApi({
    merchantGuid: userStore.merchantGuid,
    agentGuid: agentGuid.value
  })
  agentDetail.agentName = res.data.agentName
  agentDetail.agentAvatar = res.data.agentAvatar;
  commonQuestions.value = res.data.commonQuestions;
  agentDetail.agentDesc = res.data.agentDesc;
  agentDetail.price = res.data.priceYuan;
}
let historyMsgLoading = ref(true)
const queryList = async (page, pageSize) => {
  // is_all: 1
  let req = {
    merchantGuid: userStore.merchantGuid,
    sessionGuid: sessionGuid.value,
    startId: 0, //开始的聊天记录ID，当用户清除历史消息时使用
    page: page, //当前页数
    isAll: 0
  };
  try {

    let res = await getMessageHistoryApi(req);
    historyMsgLoading.value = false
    uni.setNavigationBarTitle({
      title: res.data.sessionInfo.agentInfo.agentName
    })
    let msg = [];
    if (res.data.list.length > 0) {
      msgConfig.msgId = res.data.list[0].msgId;
      res.data.list.forEach((item) => {
        if (item.chatRole === 'assistant') {
          let type = item.contentType;
          if (item.contentType === 'img') {
            type = 'image'
          }
          if (item.contentType === 'url') {
            type = 'link'
          }
          if (item.imageList === null) {
            item.imageList = [];
          }
          msg.push({
            role: 'assistant',
            content: item.chatContent,
            isSuccessData: true,
            isNewMsg: false,
            type: type,
            guid: item.guid,
            aiReplyTitle: '',
            msgId: item.lastMsgId,
            isCollected: item.isCollected,
            imageList: item.imageList
          })
        } else if (item.chatRole === 'user') {
          msg.push({
            role: 'user',
            content: item.chatContent,
            msgId: item.msgId,
          });
        }
      });
    }

    if (msg.length > 0 && msg[msg.length - 1].role === 'assistant') {
      msg[msg.length - 1].isFirst = true
    } else {
      if (isFirstLoad.value) {
        console.log('这里什么情况')
        msg.push({
          role: 'assistant',
          content: agentDetail.agentDesc,
          isSuccessData: false,
          isLoading: false,
          type: 'text',
          aiReplyTitle: '',
          isFirst: true,
          msgId: '',
          guid: 'first_none_guid',
          imageList: []
        })
        isFirstLoad.value = false;
      }
    }
    await paging.value.complete(msg)
  } catch (error) {
    historyMsgLoading.value = false
    paging.value.complete(false);
  }
};
const onOftenClick = throttle((item) => {
  //发送消息
  msgContent.value = item;
  onSend()
}, 1000)
const headImgUrl =
  'https://xiaoyi-1317629730.cos.ap-guangzhou.myqcloud.com/image/2848bb198410482f9d3e6cc343a4e51e.png';
//设置AI回复消息
const addAiChat = (answer, type = 'text', isFirst = false) => {
  //type 'richText' 'image' 'link'  'video'
  let parame = {
    content: '',
    title: '',
    imageList: [],
    // ...answer
  }
  let reMsg = {
    role: 'assistant',
    content: answer,
    isSuccessData: false,
    isLoading: true,
    type: type,
    aiReplyTitle: parame.title,
    isFirst: isFirst,
    isCollected: false,
    msgId: '',
    guid: `ai_${Date.now()}_${Math.random()}`, // 添加临时唯一标识符，后续会被服务器返回的guid替换
    imageList: parame.imageList
  };
  paging.value.addChatRecordData(reMsg);
};
//设置重置消息
const addClearChat = (answer) => {
  let reMsg = {
    role: 'assistant',
    content: answer,
    isSuccessData: false,
    isLoading: false,
    type: 'text',
    aiReplyTitle: '',
    isFirst: true,
    isCollected: false,
    msgId: '',
    guid: 'first_none_guid',
    imageList: []
  };
  paging.value.addChatRecordData(reMsg);
};
//设置user发送消息
const addUserChat = (content) => {
  let reMsg = {
    role: 'user',
    content: content,
    msgId: '',
    isLoading: true,
    type: 'text',
    imgs: [],
    guid: `user_${Date.now()}_${Math.random()}`, // 添加唯一标识符
  };
  if (msgUserReqParame.contentType === 'image') {
    reMsg.type = 'image';
    reMsg.imgs = [...msgUserReqParame.imgs];
  }
  paging.value.addChatRecordData(reMsg);
};
const onGotoMini = (appid) => {
  uni.navigateToMiniProgram({
    appId: appid,
    success(res) {
      // 打开成功
      console.log(res, 'success')
    },
    fail(res) {
      console.log(res, 'fail')
    }
  })
}

// 聊天输入框内容
const msgContent = ref('');
//聊天全局配置
const msgConfig = reactive({
  isNewGlobalMsg: false,
  nowAiMsgIndex: 0,
  msgId: '',
  nowChatLunciGuid: ''
})
let isMsgInput = ref(true)
const onMsgFocus = () => {
  isMsgInput.value = false
};
const onMsgBlur = () => {
  isMsgInput.value = true
};
const convertStringToArr = (str) => {
  // 格式化返回的流式数据 这个方法也可以提出去 我这里方便展示'
  let arr = []
  str.trim().split('\n').forEach((item) => {
    if (item.length > 0 && item) {
      const [key, value] = item.split(': ');
      if (value && value.trim()) {
        arr.push({
          [key]: value
        })
      }
    }
  });
  return arr;
}
const base64ToUtf8 = (base64String) => {
  // new TextDecoder() 小程序真机中没有这个方法，得下载一个这个 text-encoding
  // npm install text-encoding --save-dev
  // 引入import { TextDecoder } from "text-encoding/lib/encoding"; 
  const bytes = uni.base64ToArrayBuffer(base64String);
  const utf8String = new TextDecoder().decode(bytes);
  return utf8String;
}
const globalTimer = ref('')
const showTime = ref(30)
const createContact = async () => {
  let alltext = '';
  let isalltext = false;
  let isstarted = true;

  // 清除之前的定时器，防止多个定时器同时运行
  if (globalTimer.value) {
    clearInterval(globalTimer.value);
    globalTimer.value = '';
  }
  // https://xyapi.cloneyou.cn/ https://ai-api.deepcity.cn/ &conversation_id=${conversation_id.value}
  let url = `${base.baseUrl}/useragent/api.AiAgentChat/sendOpen?msgId=${msgConfig.msgId}&chatLunciGuid=${sessionGuid.value}&merchantGuid=${userStore.merchantGuid}&conversation_id=${conversation_id.value}`
  const requestTask = wx.request({
    url: url,
    method: 'GET',
    enableChunked: true,
    timeout: 600000,
  })

  requestTask.onChunkReceived((res) => {
    try {
      if (isstarted) {
        isstarted = false;
        //设置 是否是AI回答列表中的最新一条
        // dataList.value[msgConfig.nowAiMsgIndex].isNewMsg = false
        let temp = '' //临时显示文本
        let index = 0 //初始下标为0
        globalTimer.value = setInterval(async () => {
          alltext = alltext.replace(/\\n/g, '\n')
          let previousTemp = temp;
          if (temp.length < alltext.length) {
            //如果比原始文本短
            temp += alltext[index] //就往临时显示文本添加一个字符
            index = index + 1 //下标+1
          } else {
            if (isalltext) {
              //清除定时器
              isSending.value = false
              clearInterval(globalTimer.value)
              globalTimer.value = ''
              msgAiReqParame.content = temp;
              //更新msgID
              msgAiReqParame.lastMsgId = msgConfig.msgId;
              msgAiReqParame.contentType = "richText";
              msgAiReqParame.sessionGuid = sessionGuid.value;
              dataList.value[msgConfig.nowAiMsgIndex].isLoading = false;
              try {
                let msgReqRes = await saveMsgApi(msgAiReqParame);
                if (msgReqRes.code !== 0) {
                  msgConfig.isNewGlobalMsg = false;
                  dataList.value[msgConfig.nowAiMsgIndex].content = 'e-2服务繁忙，请稍候重试';
                  return;
                }
                msgConfig.msgId = msgReqRes.data.msgId;
                msgConfig.isNewGlobalMsg = false;
                dataList.value[msgConfig.nowAiMsgIndex].guid = msgReqRes.data.messageGuid;
                dataList.value[msgConfig.nowAiMsgIndex].isSuccessData = true;
                nextTick(() => {
                  paging.value.scrollToBottom();
                });
              } catch (error) {
                if (error.code === 900001) {
                  ended.value = true;
                  isSending.value = false
                  return
                }
              }

            }
          }
          //只有当内容发生变化时才更新显示文本，减少不必要的DOM更新
          if (temp !== previousTemp) {
            dataList.value[msgConfig.nowAiMsgIndex].content = temp
          }
        }, showTime.value)
      }
      let arrayBuffer = res.data; // 接收持续返回的数据
      let uint8Array = new Uint8Array(arrayBuffer);
      let text = uni.arrayBufferToBase64(uint8Array);
      text = base64ToUtf8(text);
      let textArr = convertStringToArr(text)
      console.log(textArr, 'textArr');
      textArr.map((v) => {
        if (v.hasOwnProperty('data') && v.data) {
          // 这里的xx为流式传输的关键词  如果有多个关键词 需要写多个if判断
          if (v.data == '[DONE]') {
            conversation_id.value = textArr[textArr.length - 2].id;
            isalltext = true;
            requestTask.abort()
            return
          }
          if (alltext === '') {
            alltext = v.data.replace(/^\n+/, '')
          } else {
            alltext += v.data
          }
        }
      })
    } catch (error) {
      console.log(error, 'onChunkReceivederror')
      msgConfig.isNewGlobalMsg = false;
      dataList.value[msgConfig.nowAiMsgIndex].content = 'e-3服务繁忙，请稍候重试';
      dataList.value[msgConfig.nowAiMsgIndex].isSuccessData = true;
      clearInterval(globalTimer.value)
    }


  });
};
let msgUserReqParame = reactive({
  merchantGuid: userStore.merchantGuid,
  sessionGuid: '',
  role: 'user',
  content: '',
  lastMsgId: '',
  contentType: 'text',
  sceneValue: "",
  chatLunciGuid: "",
  imgs: []
})
let msgAiReqParame = reactive({
  merchantGuid: userStore.merchantGuid,
  role: 'assistant',
  sessionGuid: '',
  content: '',
  lastMsgId: '',
  contentType: '',
  sceneValue: "",
  chatLunciGuid: "",
  imgs: [],
})
const onViceoSend = () => {
  onSend()
}

let isFirstSend = ref(false)
const onSend = async () => {
  if (msgContent.value.trim().length === 0) {
    wx.showToast({
      title: "请输入问题 ...",
      icon: "none",
      duration: 1000
    });
    return;
  }
  if (isSending.value) {
    wx.showToast({
      title: "回答中...",
      icon: "none",
      duration: 1000
    });
    return
  }
  isSending.value = true

  // 清除之前的定时器，防止多个定时器同时运行
  if (globalTimer.value) {
    clearInterval(globalTimer.value);
    globalTimer.value = '';
  }
  //写入用户发送的消息
  addUserChat(msgContent.value)
  addAiChat(' ')
  msgConfig.nowAiMsgIndex = 0;
  msgUserReqParame.content = msgContent.value;
  msgUserReqParame.lastMsgId = msgConfig.msgId;
  msgUserReqParame.sessionGuid = sessionGuid.value;
  // try {
  //   let msgReqRes = await saveMsgApi(msgUserReqParame);
  //   isFirstSend.value = true;
  //   console.log(msgReqRes, '----------msgReqRes')
  //   if (msgReqRes.code !== 0) {

  //     dataList.value[msgConfig.nowAiMsgIndex].content = 'e-2服务繁忙，请稍候重试';
  //     isSending.value = false
  //     return;
  //   }
  //   msgConfig.msgId = msgReqRes.data.msgId;
  //   dataList.value[msgConfig.nowAiMsgIndex + 1].msgId = msgReqRes.data.msgId;
  //   msgContent.value = "";
  //   createContact()
  // } catch (error) {
  //   console.log(error, '---------error');
  //   isSending.value = false;
  //   dataList.value[msgConfig.nowAiMsgIndex].isLoading = false;
  //   if (error.code === 900001) {
  //     dataList.value[msgConfig.nowAiMsgIndex].content = '试用次数已用完，请订阅激活';
  //     ended.value = true;
  //     return
  //   } else {
  //     dataList.value[msgConfig.nowAiMsgIndex].content = `${error.msg}-${error.code}`;
  //   }
  // }
};


const onMsgLink = (link) => {
  uni.navigateTo({
    url: '/pages/webview/webview',
    success: (res) => {
      res.eventChannel.emit('urlEvent', decodeURIComponent(link));
    },
    fail(res) {
      console.log(res,)
    }
  });
}


// const getAuthSetting = () => {
//   wx.getSetting({
//     success(res) {
//       if (!res.authSetting['scope.record']) {
//         authPopup.value.open();
//       } else {
//         recordConfig.isRecorderMode = !recordConfig.isRecorderMode;
//       }
//     }
//   })
// }
const authCallback = (res) => {
  authPopup.value.close();
}

//点击预览大图
const onLookImg = (urls, isMore, index) => {
  let img = [];
  if (isMore) {
    img = urls;
  } else {
    img[0] = urls
  }
  uni.previewImage({
    current: index,
    urls: img,
    longPressActions: {
      itemList: ['发送给朋友', '保存图片', '收藏'],
      success: function (data) { },
      fail: function (err) {
        console.log(err.errMsg);
      },
    },
  });
}


//进入发送语音模式
const recordConfig = reactive({
  recorderMode: 1, // 1显示 按住说话 2显示 说话中
  isRecorderMode: false, //是否显示 语音输入按钮
  voiceText: '按住说话',
  voiceTitle: '松手结束录音',
  // delShow: false,
  sendLock: true, //发送锁，当为true时上锁，false时解锁发送
  isCloseSend: false, //是否取消发送
  // time: 0, //录音时长
  // tempFilePath: '', //音频路径
  duration: 60000, //录音最大值ms 60000/1分钟
  startPoint: null, //记录长按录音开始点信息,用于后面计算滑动距离。
  isAnalyzeDisabled: false, //是否在文本解析过程中
  recording: false // 正在录音
})
// const onRecorderMode = () => {
//   getAuthSetting()
// }
// 长按录音事件
const onLongTap = (e) => {
  if (!recorderPermission.value) {
    wx.showToast({
      title: "当前版本不支持,或您未打开麦克风使用权限",
      icon: "none",
      duration: 1000
    });
    return;
  }
  if (recordConfig.isAnalyzeDisabled) {
    wx.showToast({
      title: "正在识别语音中...",
      icon: "none",
      duration: 1000
    });
    return;
  }
  if (msgConfig.isNewGlobalMsg) {
    wx.showToast({
      title: "正在回答中...",
      icon: "none",
      duration: 1000
    });
    return;
  }

  recordConfig.startPoint = e.touches[0];
  recordConfig.recorderMode = 2;
  recordConfig.voiceText = '说话中...';
  recordConfig.recording = true;
  //插件开始录音
  manager.start({
    duration: 60000,
    lang: 'zh_CN'
  })
  recordConfig.sendLock = false;
}
//长按松开录音事件
const onTouchend = () => {
  if (recordConfig.isCloseSend) {
    recordInit()
    return;
  }
  if (recordConfig.isAnalyzeDisabled) {
    return;
  }
  if (msgConfig.isNewGlobalMsg) {
    return;
  }
  recordInit()
  if (!recordConfig.recording) {
    return
  }
  //插件停止录音
  manager.stop()
  recordConfig.isAnalyzeDisabled = true;
  if (!recordConfig.sendLock) {
    uni.showLoading({
      title: '识别中',
      mask: true,
    })
  }
}

// 删除录音
const onTouchMove = (e) => {
  if (!e || !e.touches || e.touches.length === 0 || !recordConfig.startPoint) return;
  //touchmove时触发
  let moveLenght = e.touches[e.touches.length - 1].clientY - recordConfig.startPoint.clientY; //移动距离
  if (Math.abs(moveLenght) > 30) {
    recordConfig.voiceTitle = "松开手指,取消发送";
    recordConfig.voiceText = '松开手指,取消发送';
    recordConfig.sendLock = true; //触发了上滑取消发送，上锁
    recordConfig.isCloseSend = true; //触发了上滑取消发送 改变样式
  } else {
    recordConfig.voiceTitle = "松手结束录音";
    recordConfig.voiceText = '松手结束录音';
    recordConfig.sendLock = false; //上划距离不足，依然可以发送，不上锁
    recordConfig.isCloseSend = false; //触发了上滑取消发送 改变样式
  }
}
//初始化录音配置
const recordInit = () => {
  // recordConfig.delShow = false;
  // recordConfig.time = 0;
  // recordConfig.tempFilePath = '';
  recordConfig.recorderMode = 1;
  recordConfig.voiceText = '按住说话';
  recordConfig.voiceTitle = '松手结束录音';
  recordConfig.isCloseSend = false;
}

const initRecord = () => {
  //有新的识别内容返回，则会调用此事件
  manager.onRecognize = (res) => {
    if (recordConfig.sendLock) {
      return
    } else {
      msgContent.value = res.result;
      onViceoSend()
    }
  }

  // 识别结束事件
  manager.onStop = (res) => {
    console.log('onStoponStop', res)
    recordConfig.isAnalyzeDisabled = false;
    recordConfig.recording = false;
    if (recordConfig.sendLock) {
      return
    }
    uni.hideLoading();
    let text = res.result;
    if (text == '') {
      // wx.showToast({
      // 	title: "未识别到内容",
      // 	icon: "none",
      // 	duration: 1000
      // });
      return
    }
    msgContent.value = text;
    onViceoSend()
  }

  // 识别错误事件
  manager.onError = (res) => {
    uni.hideLoading();
    recordConfig.isAnalyzeDisabled = false;
    recordConfig.recording = false;
    let code = res.retcode;
    switch (code) {
      case -30003:
        wx.showToast({
          title: "未检测到语音",
          icon: "none",
          duration: 1000
        });
        break;
      default:
        wx.showToast({
          title: "未检测到语音~",
          icon: "none",
          duration: 1000
        });
        break;
    }
  }
}


//获取麦克风权限
const getRecordPer = () => {
  wx.getSetting({
    success(res) {
      if (!res.authSetting['scope.record']) {
        wx.authorize({
          scope: 'scope.record',
          success() {
            recorderPermission.value = true;
          },
          fail() {
            recorderPermission.value = false;
          },
        });
      } else {
        recorderPermission.value = true;
      }
    },
  });
}
// // 显示订阅弹窗
const showSubscribePopup = () => {
  showSubscribeModal.value = true
}

// // 关闭订阅弹窗
const closeSubscribePopup = () => {
  showSubscribeModal.value = false
}
const handleSubscribeConfirm = async () => {
  let payInfo = await createPurchaseOrderApi({
    merchantGuid: userStore.merchantGuid,
    agentGuid: agentGuid.value,
    payEnv: 'xcx'
  })
  miniPay(payInfo.data.payInfo).then(
    async res => {
      queryPayChatStauts(payInfo.data.orderNo, queryStatusNum);
    },
    res => {
      uni.showToast({
        title: res.msg,
      });
    }
  );
}
const queryPayChatStauts = async (orderNo, number) => {
  number++;
  try {
    let orderInfo = await queryPurchaseOrderApi({
      orderNo,
    });
    if (orderInfo.data.isPaid) {
      uni.showToast({
        title: '支付成功',
      });
      ended.value = false
      showSubscribeModal.value = false
    } else {
      if (number > 12) {
        uni.showToast({
          title: '支付失败',
        });
      } else {
        queryPayChatStauts(orderNo, number);
      }
    }
  } catch (e) {
    uni.showToast({
      title: e.msg ? e.msg : '支付失败',
    });
  }
};
// 上传图片
const uploading = ref(false)
const chooseimg = () => {
  if (uploading.value) return
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      const tempFilePath = res.tempFilePaths[0]
      await uploadImg(tempFilePath)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
    }
  })
}

const uploadImg = async (filePath) => {
  try {
    uploading.value = true
    uni.showLoading({
      title: '上传中...',
      mask: true
    })

    const uploadRes = await updataFileFun(filePath)
    const result = JSON.parse(uploadRes.data)

    if (result.code === 0) {
      msgUserReqParame.imgs[0] = result.data;
      msgUserReqParame.contentType = 'image';
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      })
    } else {
      throw new Error(result.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  } finally {
    uploading.value = false
    uni.hideLoading()
  }
}

onLoad(async (params) => {
  // #ifdef MP-WEIXIN
  getRecordPer();
  initRecord();
  // #endif
  if (params.sessionGuid) {
    agentGuid.value = params.sessionGuid;
    await subscribeAgent()
    await getAgentDetail()
  }
  if (userStore.userToken) {
    nextTick(() => {
      if (paging.value) {
        paging.value.reload()
      }
    })
  }
});
</script>
<style scoped lang="scss">
.container {
  background: #f7f7f7;
  min-height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 518rpx;
  width: 100%;
  overflow-x: hidden;
}

.onAdminEdit {
  // position: absolute;
  // left: 0;
  // top: 0;
  width: 100%;
  height: 80rpx;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-end;
  align-items: center;

  .edit-btn {
    border-radius: 10rpx;
    border: 2rpx solid #333333;
    padding: 2rpx 10rpx;
    width: fit-content;
    margin-right: 20rpx;
    font-size: 22rpx;

    &.on {
      color: #FA5151;
      border-color: #FA5151;
    }
  }
}

.auth-pop-box {
  width: 680rpx;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8rpx;

  .title {
    font-size: 28rpx;
    text-align: center;
  }

  .btn {
    margin-top: 20px;
    width: 100%;
    height: 90rpx;
    background: linear-gradient(90deg, #8d40f8 0%, #5e24f5 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 12rpx;
    font-weight: 600;
    font-size: 30rpx;
    color: #ffffff;
  }
}

// .spinner {
//   --accent: #ffffff;
//   --max-scale: 4;
//   --speed: 0.2;
//   display: flex;
//   gap: 0.5em;
//   transform: skew(15deg, 10deg);
//   margin-top: 40rpx;
// }

// .spinner .sub-spinner {
//   display: block;
//   background-color: var(--accent);
//   box-shadow: 1px 1px 5px 0.2px var(--accent);
//   width: 1px;
//   height: 0.6em;
// }

// .spinner .spinner-part-0 {
//   animation: load432 calc(1s/var(--speed)) linear infinite;
// }

// .spinner .spinner-part-1 {
//   animation: load432 calc(0.16s/var(--speed)) linear infinite;
// }

// .spinner .spinner-part-2 {
//   animation: load432 calc(0.4s/var(--speed)) linear infinite;
// }

// .spinner .spinner-part-3 {
//   animation: load432 calc(0.5s/var(--speed)) linear infinite;
// }

// @keyframes load432 {
//   50% {
//     transform: scaleY(var(--max-scale));
//   }
// }

// .recode-box {
//   width: 0;
//   height: 0;
//   background-color: rgba(0, 0, 0, .6);
//   position: fixed;
//   z-index: 10;
//   bottom: 0;
//   left: 0;
//   overflow: hidden;

//   &.record-layer {
//     width: 100vw;
//     height: 100vh;
//   }

//   .recode-loading-box {
//     width: 70vw;
//     height: 200rpx;
//     color: #ffffff;
//     border-radius: 10rpx;
//     background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);
//     position: absolute;
//     bottom: 300rpx;
//     left: 50%;
//     transform: translateX(-50%);
//     display: flex;
//     flex-direction: column;
//     justify-content: space-around;
//     align-items: center;
//     transition: all 0.3s;

//     &.is-close-send {
//       background: linear-gradient(136deg, #7e2c2c 0%, #ac3838 100%)
//     }

//     .spinner-title {
//       margin-top: 40rpx;
//       font-size: 24rpx;
//     }
//   }
// }


.on-load-more-box {
  text-align: center;
  padding: 20rpx 0;
  color: #66648a;
  font-size: 24rpx;
}

.chat-main {
  padding-left: 20rpx;
  padding-right: 20rpx;
  padding-top: 40rpx;
  display: flex;
  flex-direction: column;
  // padding-bottom: 202rpx;
}

.agent-logo {
  display: flex;
  justify-content: center;
  margin: 30px 0 20px 0;

  .logo {
    display: block;
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
  }
}

.chat-ls {
  padding-bottom: 20px;
  position: relative;

  .msg-m {
    display: flex;
    padding: 20rpx 0;
    width: 100%;

    .logo-box {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      box-sizing: border-box;

      .user-img {
        flex: none;
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .msg-text {
      font-size: 32rpx;
      line-height: 44rpx;
      padding: 18rpx 24rpx;
      box-sizing: border-box;

      .img-list-box {
        margin-top: 10rpx;
        // display: flex;
        // flex-wrap: wrap;
        // justify-content: space-between;

        &.noWrap {
          .img {
            flex: 1;
            // width: 48%;
            height: auto;
          }
        }

        .img {
          max-width: 100%;
          height: auto;
          display: block;
        }
      }

      .rich-text-box {
        .t {
          word-wrap: break-word;
        }

        .richImg {
          width: 100% !important;
          height: auto !important;
        }

        .often-questions {
          margin-top: 30rpx;
          // position: relative;

          .item {
            color: #000;
            font-size: 28rpx;
            padding-left: 30rpx;
            position: relative;

            &::before {
              content: '';
              display: block;
              width: 10rpx;
              height: 10rpx;
              background-color: #000;
              border-radius: 50%;
              position: absolute;
              left: 8rpx;
              top: 50%;
              transform: translateY(-50%);
            }
          }

        }

      }



      .img-box {
        // margin-top: 10rpx;

        .img {
          max-width: 510rpx;
          // height: 300rpx;
          display: block;
        }
      }
    }
  }

  .msg-left {
    flex-direction: row;
    position: relative;

    .msg-text {
      margin-left: 16rpx;
      font-size: 32rpx;
      // background-color: rgba(247, 247, 247, 1);
      // background: linear-gradient(254deg, rgba(76, 106, 254, 0.1), rgba(91, 118, 254, 0.1));
      background-color: #F9F9F9;
      box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(35, 0, 131, 0.05);
      border-radius: 0rpx 20rpx 20rpx 20rpx;
      max-width: 680rpx;
      color: #333333;
      margin-top: 50rpx;
      position: relative;

      &.hasReply {
        margin-top: 0px;
      }

      .text-box {
        .link {
          word-break: break-all;
          text-decoration: underline;
          border-bottom: 1px solid #66648a;
        }
      }

      .video-box {
        width: 512rpx;

        .video-dom {
          width: 100%;
          height: 300rpx;
        }
      }

      .mini-box {
        width: 512rpx;

        .btn {
          width: 100%;
          font-size: 26rpx;
          height: 70rpx;
          line-height: 70rpx;
          text-align: center;
          transition: background 0.3s;
          color: #ffffff;
          background: linear-gradient(90deg, #8D40F8 0%, #5E24F5 100%);
          border-radius: 12rpx 12rpx 12rpx 12rpx;
        }
      }
    }

    .other-box {
      border-top: 1px solid #ECECEC;
      padding-top: 20rpx;
      margin-top: 20rpx;
      display: flex;

      .icon {
        display: block;
        width: 60rpx;
        height: 60rpx;
        margin-right: 8rpx;
      }

      .text-btn {
        background-color: #E7EDFA;
        color: #2963F6;
        font-size: 24rpx;
        height: 60rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 16rpx;
        margin-left: auto;
      }
    }

    .ai-reply-msg {
      color: #66648a;
      font-size: 26rpx;
      padding-left: 16rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      position: absolute;
      top: -50rpx;
      left: 0;
    }


  }

  .msg-right {
    flex-direction: row-reverse;
    position: relative;

    .msg-text {
      margin-right: 16rpx;
      font-size: 32rpx;
      // background: linear-gradient(136deg, #7531f6 0%, #686bf2 100%);
      color: #fff;
      background-color: #5380F2;
      box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(35, 0, 131, 0.05);
      border-radius: 20rpx 0rpx 20rpx 20rpx;
      // border-radius: 10rpx;
      max-width: 560rpx;
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}

//试用结束
.sy-end-box {
  padding: 20px 0 30px 0;
  background-color: #fff;
  font-size: 30rpx;
  text-align: center;
  color: #2A64F6;
  z-index: 9;

  .end-btn {
    width: 530rpx;
    margin: 20px auto 0 auto;
    height: 90rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #2A64F6;
    border-radius: 50rpx 50rpx 50rpx 50rpx;
    color: #fff;
  }
}

.submit-box {
  width: 100%;
  font-size: 30rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  padding-top: 20rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
  /*  #ifdef MP-WEIXIN */
  /* 为兼容某些Android版本微信小程序，添加最小padding保障 */
  padding-bottom: calc(env(safe-area-inset-bottom, 40rpx) - 20rpx);
  /* 降级方案：当env()不支持时使用固定值 */
  padding-bottom: 40rpx;
  // padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  /*  #endif  */
  /*  #ifdef H5 */
  padding-bottom: 20rpx;
  /*  #endif  */

  .util-question-box {
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    margin-top: 20rpx;

    .item {
      display: flex;
      background-color: #efefff;
      border-radius: 12rpx;
      padding: 19rpx;
      margin-right: 20rpx;
      align-items: center;
      width: fit-content;
      flex: 0 0 auto;

      .icon {
        display: block;
        width: 32rpx;
        height: 32rpx;
      }

      .text {
        margin-left: 6rpx;
        font-size: 26rpx;
        background: linear-gradient(95deg, #40aaf8 0%, #6732f6 52%, #5e24f5 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }

  .sub-input-box {
    display: flex;
    align-items: center;
    border-radius: 12rpx;
    // border: 4rpx solid #686bf2;
    padding: 10rpx 0px;
    background-color: #F7F7F7;
    position: relative;

    &.mt {
      margin-top: 20rpx;
    }

    .recode-loading-box {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);
      justify-content: center;
      align-items: center;
      border-radius: 12rpx;

      &.is-close-send {
        background: linear-gradient(136deg, #d45757 0%, #ca3333 100%)
      }
    }

    .spinner-title {
      position: absolute;
      width: 100%;
      text-align: center;
      top: -40px;
      left: 0;
      z-index: 9;
    }

    .spinner {
      --accent: #ffffff;
      --max-scale: 4;
      --speed: 0.2;
      display: flex;
      gap: 0.5em;
    }

    .spinner .sub-spinner {
      display: block;
      background-color: var(--accent);
      box-shadow: 1px 1px 5px 0.2px var(--accent);
      width: 1px;
      height: 0.4em;
    }

    .spinner .spinner-part-0 {
      animation: load432 calc(1s/var(--speed)) linear infinite;
    }

    .spinner .spinner-part-1 {
      animation: load432 calc(0.16s/var(--speed)) linear infinite;
    }

    .spinner .spinner-part-2 {
      animation: load432 calc(0.4s/var(--speed)) linear infinite;
    }

    .spinner .spinner-part-3 {
      animation: load432 calc(0.5s/var(--speed)) linear infinite;
    }

    @keyframes load432 {
      50% {
        transform: scaleY(var(--max-scale));
      }
    }
  }


  .voice-box {
    .icon {
      width: 60rpx;
      height: 60rpx;
      display: block;
    }
  }

  .send-record-box {
    width: 100%;
    // flex: 1;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    transition: background 0.3s;
    // height: 90rpx;
    color: #ffffff;
    background-color: #333333;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .icon {
      display: block;
      height: 60rpx;
      width: 60rpx;
    }

    &.longPress {
      color: #ffffff;
      padding-top: 10rpx;
      width: 1500rpx;
      position: fixed;
      left: -50%;
      height: 750rpx;
      background: linear-gradient(136deg, #7531F6 0%, #686BF2 100%);
      z-index: 11;
      bottom: 0;
      transform: translateY(75%);
      border-radius: 50%;
    }

    &.is-close-send {
      background: linear-gradient(136deg, #f63131 0%, #f26868 100%)
    }
  }

  .input-box {
    width: 100%;
    display: flex;
    align-items: center;
    // background-color: #ccc;
    // padding: 5rpx;
    min-height: 60rpx;
    max-height: 300rpx;
    box-sizing: border-box;

    .textarea-box {
      flex: 1;
      // border: 1px solid #f2f2f2;
      min-height: 60rpx;
      // background-color: #f2f2f2;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20rpx;
      border-radius: 20rpx;
    }

    .textarea-input {
      width: 100%;
      color: #333333;
      max-height: 60px;
      overflow-y: scroll;
      box-sizing: border-box;
      font-size: 26rpx;
    }

    .input {
      flex: 1;
      color: #333333;
      height: 60rpx;
      line-height: 60rpx;
    }

    // .btn-box {
    //   display: flex;
    //   width: fit-content;

    //   // width: 60rpx;
    //   // height: 60rpx;
    //   .upload {
    //     margin-right: 12rpx;
    //   }

    //   .icon {
    //     width: 60rpx;
    //     height: 60rpx;
    //     display: block;
    //   }
    // }

    .vicoe-box {
      display: flex;
      width: fit-content;
      gap: 6px;

      .icon {
        width: 60rpx;
        height: 60rpx;
        display: block;
      }

      .upload {
        margin-right: 12rpx;
        position: relative;
      }

    }
  }


}

.clear-msg {
  color: #66648a;
  font-size: 30rpx;
  z-index: 99;
  text-align: right;
  margin-bottom: 6px;
  position: absolute;
  width: 100%;
  right: 0px;
  bottom: 140rpx;
  z-index: 8;

  .txt {
    margin-right: 20px;
  }
}

// 三个闪烁的加载小圆点
.loading {
  display: flex;
  align-items: center;
  margin-top: 10rpx;

  .loading-dot {
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background-color: #999;
    margin-right: 6rpx;
    animation: loading-blink 1.4s infinite both;

    &:nth-child(1) {
      animation-delay: 0s;
    }

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
      margin-right: 0;
    }
  }
}

@keyframes loading-blink {

  0%,
  80%,
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }

  40% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>