"use strict";const e=require("../../common/vendor.js"),a=require("../../common/assets.js"),t=require("../../api/index.js"),o=require("../../stores/user.js"),n=require("../../api/common.js"),i={__name:"index",setup(i){const l=o.useUserStore(),s=e.reactive({merchantGuid:l.merchantGuid,agentName:"",agentType:1,categoryGuid:"",promptContent:"",agentDesc:"",agentAvatar:"",isPaid:0,price:"",trial_chat_count:0,isPublic:1,agentConfig:{deploy_address:"",coze_sign:"",secret_token:"",secret_key_type:"default"},knowledgeBaseIds:[],welcomeMessage:"",commonQuestions:["","",""]}),u=e.ref(!1),r=e.ref(!1),c=e.ref(!1),d=e.ref(!1),g=e.ref(""),v=e.ref(null),m=e.ref([]),p=e.ref(!1),y=e.ref(null),f=e.ref(""),h=e.computed((()=>f.value?m.value.filter((e=>e.categoryName.toLowerCase().includes(f.value.toLowerCase()))):m.value)),w=e.ref([{label:"内部",value:1},{label:"dify",value:2},{label:"coze",value:3}]),_=e.ref([{label:"自定义输入",value:"default"},{label:"读取用户保存密钥",value:"user"}]);e.ref("default");const T=e.ref([{label:"免费",value:0},{label:"付费",value:1}]),x=()=>{console.log("跳转秘钥管理"),e.index.navigateTo({url:"/pages/create-agent/secret"})},C=()=>{r.value||e.index.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:async e=>{const a=e.tempFilePaths[0];await k(a)},fail:e=>{console.error("选择图片失败:",e)}})},k=async a=>{try{r.value=!0,e.index.showLoading({title:"上传中...",mask:!0});const t=await n.updataFileFun(a),o=JSON.parse(t.data);if(0!==o.code)throw new Error(o.msg||"上传失败");s.agentAvatar=o.data,e.index.showToast({title:"头像上传成功",icon:"success"})}catch(t){console.error("上传头像失败:",t),e.index.showToast({title:"上传失败",icon:"none"})}finally{r.value=!1,e.index.hideLoading()}},b=async()=>{if(s.agentName.trim())if(s.agentDesc.trim()){if(!c.value)try{c.value=!0,e.index.showLoading({title:"AI生成中...",mask:!0});const a={merchantGuid:l.merchantGuid,agentName:s.agentName.trim(),agentDesc:s.agentDesc.trim()},o=await t.generateAvatarApi(a);if(0!==o.code)throw new Error(o.msg||"生成失败");s.agentAvatar=o.data.data.imageUrl,e.index.showToast({title:"头像生成成功",icon:"success"})}catch(a){console.error("AI生成头像失败:",a),e.index.showToast({title:a.message||"生成失败",icon:"none"})}finally{c.value=!1,e.index.hideLoading()}}else e.index.showToast({title:"请先输入智能体描述",icon:"none"});else e.index.showToast({title:"请先输入智能体名称",icon:"none"})},A=async()=>{try{const e=await t.getCategoryListApi({merchantGuid:l.merchantGuid});0===e.code&&(m.value=e.data||[])}catch(e){console.error("获取分类列表失败:",e)}},P=()=>{0===m.value.length&&A(),p.value=!0},G=()=>{p.value=!1,f.value=""},N=()=>{},L=()=>{f.value=""},I=()=>{y.value?(v.value=y.value,s.categoryGuid=y.value.guid,G()):e.index.showToast({title:"请选择分类",icon:"none"})};e.onLoad((a=>{a.guid&&(d.value=!0,(async a=>{try{e.index.showLoading({title:"加载中...",mask:!0});const o=await t.getMyDetailApi({guid:a});if(0!==o.code)throw new Error(o.msg||"获取智能体详情失败");{const e=o.data;s.agentName=e.agentName||"",s.agentType=e.agentType||1,s.categoryGuid=e.categoryGuid||"",s.promptContent=e.promptContent||"",s.agentDesc=e.agentDesc||"",s.agentAvatar=e.agentAvatar||"",s.isPaid=e.isPaid||0,s.price=e.price||"",s.trial_chat_count=e.trialChatCount||0,s.isPublic=e.isPublic||1,s.agentConfig=e.agentConfig||{secret_token:"",secret_key_type:"default",deploy_address:""},s.knowledgeBaseIds=e.knowledgeBaseIds||[],s.welcomeMessage=e.welcomeMessage||"",s.commonQuestions=e.commonQuestions.length>0?e.commonQuestions:["","",""],e.category&&(v.value=e.category),g.value=a}}catch(o){console.error("获取智能体详情失败:",o),e.index.showToast({title:o.message||"获取智能体详情失败",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500)}finally{e.index.hideLoading()}})(a.guid))})),e.onMounted((()=>{A()}));const D=async()=>{if(!u.value)if(s.categoryGuid)if(s.agentName.trim())try{if(u.value=!0,e.index.showLoading({title:d.value?"更新中...":"创建中...",mask:!0}),1===s.isPaid&&!s.price)return e.index.showToast({title:"付费智能体请输入价格",icon:"none"}),u.value=!1,void e.index.hideLoading();const a=s.commonQuestions.filter((e=>e.trim())),o={agentName:s.agentName.trim(),agentType:s.agentType,categoryGuid:s.categoryGuid,promptContent:s.promptContent.trim(),agentDesc:s.agentDesc.trim(),agentAvatar:s.agentAvatar,isPaid:s.isPaid,price:1===s.isPaid?parseFloat(s.price):0,isPublic:1,trial_chat_count:s.trial_chat_count,agentConfig:s.agentConfig,knowledgeBaseIds:s.knowledgeBaseIds,welcomeMessage:s.welcomeMessage.trim(),commonQuestions:a};if(d.value){o.guid=g.value,console.log("更新智能体数据:",o);const a=await t.updateMyAgentApi(o);if(0!==a.code)throw new Error(a.msg||"更新失败");e.index.showToast({title:"更新成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)}else{o.merchantGuid=s.merchantGuid,console.log("创建智能体数据:",o);const a=await t.createAgentApi(o);if(0!==a.code)throw new Error(a.msg||"创建失败");e.index.showToast({title:"创建成功",icon:"success"}),setTimeout((()=>{e.index.navigateBack()}),1500)}}catch(a){console.error(d.value?"更新失败:":"创建失败:",a),e.index.showToast({title:a.msg||(d.value?"更新失败":"创建失败"),icon:"none"})}finally{u.value=!1,e.index.hideLoading()}else e.index.showToast({title:"请输入智能体名称",icon:"none"});else e.index.showToast({title:"请选择分类",icon:"none"})};return(t,o)=>e.e({a:s.agentAvatar,b:!c.value},c.value?{}:{c:a._imports_0$1},{d:c.value},(c.value,{}),{e:e.o(C),f:e.t(c.value?"AI生成中...":"AI生成形象"),g:e.o(b),h:c.value?1:"",i:e.o(x),j:e.t(v.value?v.value.categoryName:"请选择分类"),k:v.value?"":1,l:e.o(P),m:s.agentName,n:e.o((e=>s.agentName=e.detail.value)),o:s.agentDesc,p:e.o((e=>s.agentDesc=e.detail.value)),q:e.f(w.value,((a,t,o)=>e.e({a:s.agentType===a.value},(s.agentType,a.value,{}),{b:s.agentType===a.value?1:"",c:e.t(a.label),d:a.value,e:e.o((e=>{return t=a.value,void(s.agentType=t);var t}),a.value)}))),r:1===s.agentType},1===s.agentType?{s:s.promptContent,t:e.o((e=>s.promptContent=e.detail.value))}:{},{v:1!==s.agentType},1!==s.agentType?{w:e.f(_.value,((a,t,o)=>e.e({a:s.agentConfig.secret_key_type===a.value},(s.agentConfig.secret_key_type,a.value,{}),{b:s.agentConfig.secret_key_type===a.value?1:"",c:e.t(a.label),d:a.value,e:e.o((e=>{return t=a.value,void(s.agentConfig.secret_key_type=t);var t}),a.value)})))}:{},{x:3===s.agentType},3===s.agentType?{y:s.agentConfig.coze_sign,z:e.o((e=>s.agentConfig.coze_sign=e.detail.value))}:{},{A:1!==s.agentType},1!==s.agentType?{B:s.agentConfig.secret_token,C:e.o((e=>s.agentConfig.secret_token=e.detail.value))}:{},{D:2===s.agentType},2===s.agentType?{E:s.agentConfig.deploy_address,F:e.o((e=>s.agentConfig.deploy_address=e.detail.value))}:{},{G:s.welcomeMessage,H:e.o((e=>s.welcomeMessage=e.detail.value)),I:e.f(s.commonQuestions,((a,t,o)=>({a:`请输入引导问题${t+1}`,b:s.commonQuestions[t],c:e.o((e=>s.commonQuestions[t]=e.detail.value),t),d:t}))),J:e.f(T.value,((a,t,o)=>e.e({a:s.isPaid===a.value},(s.isPaid,a.value,{}),{b:s.isPaid===a.value?1:"",c:e.t(a.label),d:a.value,e:e.o((e=>{return t=a.value,void(s.isPaid=t);var t}),a.value)}))),K:1===s.isPaid},1===s.isPaid?{L:s.price,M:e.o((e=>s.price=e.detail.value))}:{},{N:1===s.isPaid},1===s.isPaid?{O:s.trial_chat_count,P:e.o((e=>s.trial_chat_count=e.detail.value))}:{},{Q:e.t(u.value?d.value?"更新中...":"创建中...":d.value?"完成":"创建AI智能体"),R:e.o(D),S:u.value?1:"",T:p.value},p.value?e.e({U:a._imports_1$6,V:e.o([e=>f.value=e.detail.value,N]),W:f.value,X:f.value},f.value?{Y:a._imports_2$5,Z:e.o(L)}:{},{aa:e.f(h.value,((a,t,o)=>{var n,i,l;return e.e({a:(null==(n=y.value)?void 0:n.guid)===a.guid},(null==(i=y.value)||i.guid,a.guid,{}),{b:(null==(l=y.value)?void 0:l.guid)===a.guid?1:"",c:e.t(a.categoryName),d:a.guid,e:e.o((e=>(e=>{y.value=e})(a)),a.guid)})})),ab:0===h.value.length},(h.value.length,{}),{ac:e.o(G),ad:e.o(I),ae:y.value?"":1,af:e.o((()=>{})),ag:e.o(G)}):{})}},l=e._export_sfc(i,[["__scopeId","data-v-019199ed"]]);wx.createPage(l);
