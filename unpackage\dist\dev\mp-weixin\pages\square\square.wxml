<view class="container data-v-6bc6c6b7"><view class="tabs-container data-v-6bc6c6b7"><uv-tabs wx:if="{{d}}" class="data-v-6bc6c6b7" u-s="{{['right']}}" bindchange="{{c}}" u-i="6bc6c6b7-0" bind:__l="__l" u-p="{{d}}"><view class="search-icon data-v-6bc6c6b7" bindtap="{{b}}" slot="right"><image src="{{a}}" class="icon data-v-6bc6c6b7" mode="aspectFit"></image></view></uv-tabs></view><view class="content-container data-v-6bc6c6b7" style="{{'height:' + l}}"><swiper class="tab-swiper data-v-6bc6c6b7" current="{{i}}" indicator-dots="{{false}}" autoplay="{{false}}" circular="{{false}}" bindchange="{{j}}" style="{{'height:' + k}}"><swiper-item wx:for="{{e}}" wx:for-item="tab" wx:key="c" class="data-v-6bc6c6b7" style="{{'height:' + h}}"><scroll-view scroll-y="true" class="scroll-view data-v-6bc6c6b7" style="{{'height:' + g}}"><view class="agent-list data-v-6bc6c6b7"><view wx:for="{{tab.a}}" wx:for-item="item" wx:key="k" class="agent-item data-v-6bc6c6b7"><view class="avatar data-v-6bc6c6b7" bindtap="{{item.b}}"><image src="{{item.a}}" class="avatar-img data-v-6bc6c6b7" mode="aspectFill"></image></view><view class="content data-v-6bc6c6b7" bindtap="{{item.g}}"><view class="title data-v-6bc6c6b7">{{item.c}} <text wx:if="{{f}}" class="data-v-6bc6c6b7">({{item.d}}/元-试用3次)</text></view><view class="description data-v-6bc6c6b7">{{item.e}}</view><view class="author data-v-6bc6c6b7">@{{item.f}}</view></view><view class="{{['action-btn', 'data-v-6bc6c6b7', item.i && 'subscribed']}}" bindtap="{{item.j}}"><text class="btn-text data-v-6bc6c6b7">{{item.h}}</text></view></view></view><view wx:if="{{tab.b}}" class="empty-container data-v-6bc6c6b7"> 暂无智能体 </view></scroll-view></swiper-item></swiper></view></view>