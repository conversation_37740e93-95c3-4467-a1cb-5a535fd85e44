"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_index = require("../../api/index.js");
const stores_user = require("../../stores/user.js");
const _sfc_main = {
  __name: "video-create",
  setup(__props) {
    const userStore = stores_user.useUserStore();
    const showVideoModal = common_vendor.ref(false);
    const activePrimaryTab = common_vendor.ref(0);
    const primaryTabs = common_vendor.ref(["模板", "定制数字人"]);
    const selectedTemplate = common_vendor.ref(null);
    const templateList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const isLoadingMore = common_vendor.ref(false);
    const hasMoreData = common_vendor.ref(true);
    const showVoiceModal = common_vendor.ref(false);
    const activeVoiceTab = common_vendor.ref(0);
    const voiceTabs = common_vendor.ref(["我的", "全部", "男性", "女性"]);
    const selectedVoice = common_vendor.ref(null);
    const currentVoiceText = common_vendor.ref("情感女生");
    const currentAvatar = common_vendor.ref("");
    const showSubtitle = common_vendor.ref(true);
    const showPrivacyModal = common_vendor.ref(false);
    const privacyContent = common_vendor.ref("");
    const canConfirm = common_vendor.ref(false);
    const countdown = common_vendor.ref(3);
    const textContent = common_vendor.ref("");
    const loadTemplateList = async (page = 1, isLoadMore = false, autoSelectFirst = false) => {
      if (isLoadingMore.value && isLoadMore)
        return;
      try {
        if (isLoadMore) {
          isLoadingMore.value = true;
        }
        let res;
        if (activePrimaryTab.value === 0) {
          res = await api_index.commonPersonListApi({
            merchantGuid: userStore.merchantGuid,
            page,
            pageSize: pageSize.value
          });
        } else {
          res = await api_index.getMyPersonListApi({
            merchantGuid: userStore.merchantGuid,
            page,
            pageSize: pageSize.value
          });
        }
        const newList = res.data.list || [];
        common_vendor.index.__f__("log", "at pages/my/video-create.vue:270", newList);
        if (page === 1) {
          templateList.value = newList;
          common_vendor.index.__f__("log", "at pages/my/video-create.vue:275", autoSelectFirst);
          common_vendor.index.__f__("log", "at pages/my/video-create.vue:276", templateList.value);
          if (autoSelectFirst && newList.length > 0) {
            common_vendor.index.__f__("log", "at pages/my/video-create.vue:280", newList[0]);
            selectedTemplate.value = newList[0];
            currentAvatar.value = getTemplateImage(newList[0]);
            common_vendor.index.__f__("log", "at pages/my/video-create.vue:283", currentAvatar.value);
            common_vendor.index.__f__("log", "at pages/my/video-create.vue:284", selectedTemplate.value);
          }
        } else {
          templateList.value = [...templateList.value, ...newList];
        }
        const pageInfo = res.data.pageInfo || {};
        hasMoreData.value = page < (pageInfo.totalPage || 1);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/video-create.vue:296", "获取模板列表失败:", error);
        common_vendor.index.showToast({
          title: "加载失败",
          icon: "none"
        });
      } finally {
        if (isLoadMore) {
          isLoadingMore.value = false;
        }
      }
    };
    const loadMoreTemplates = () => {
      if (!hasMoreData.value || isLoadingMore.value)
        return;
      currentPage.value++;
      loadTemplateList(currentPage.value, true);
    };
    const getTemplateImage = (template) => {
      var _a, _b;
      if (activePrimaryTab.value === 0) {
        return (_b = (_a = template.figures) == null ? void 0 : _a[0]) == null ? void 0 : _b.cover;
      } else {
        return template.picUrl;
      }
    };
    const getImageInfo = async (imageSrc) => {
      try {
        const result = await common_vendor.index.getImageInfo({
          src: imageSrc
        });
        common_vendor.index.__f__("log", "at pages/my/video-create.vue:333", "图片信息", result);
        return {
          width: result.width,
          height: result.height,
          path: result.path,
          orientation: result.orientation,
          type: result.type
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/video-create.vue:342", "获取图片信息失败:", error);
        return null;
      }
    };
    const allVoices = common_vendor.ref([
      { id: 1, name: "说话大爷", category: "my", type: "male" },
      { id: 2, name: "做饭小哥", category: "all", type: "male" },
      { id: 3, name: "爽朗阿姨", category: "all", type: "female" },
      { id: 4, name: "高冷学妹", category: "all", type: "female" },
      { id: 5, name: "激情朗诵小哥", category: "all", type: "male" },
      { id: 6, name: "演讲女孩", category: "all", type: "female" },
      { id: 7, name: "做饭小哥", category: "all", type: "male" },
      { id: 8, name: "爽朗阿姨", category: "all", type: "female" },
      { id: 9, name: "高冷学妹", category: "all", type: "female" },
      { id: 10, name: "科普男声", category: "all", type: "male" },
      { id: 11, name: "演讲女孩", category: "all", type: "female" },
      { id: 12, name: "做饭小哥", category: "all", type: "male" },
      { id: 13, name: "爽朗阿姨", category: "all", type: "female" },
      { id: 14, name: "高冷学妹", category: "all", type: "female" },
      { id: 15, name: "科普男声", category: "all", type: "male" },
      { id: 16, name: "演讲女孩", category: "all", type: "female" },
      { id: 17, name: "做饭小哥", category: "all", type: "male" },
      { id: 18, name: "爽朗阿姨", category: "all", type: "female" },
      { id: 19, name: "高冷学妹", category: "all", type: "female" },
      { id: 20, name: "科普男声", category: "all", type: "male" }
    ]);
    const currentVoices = common_vendor.computed(() => {
      if (activeVoiceTab.value === 0) {
        return allVoices.value.filter((v) => v.category === "my");
      } else if (activeVoiceTab.value === 1) {
        return allVoices.value;
      } else if (activeVoiceTab.value === 2) {
        return allVoices.value.filter((v) => v.type === "male");
      } else {
        return allVoices.value.filter((v) => v.type === "female");
      }
    });
    const openVideoModal = () => {
      showVideoModal.value = true;
      selectedTemplate.value = null;
      activePrimaryTab.value = 0;
      currentPage.value = 1;
      hasMoreData.value = true;
      loadTemplateList(1);
    };
    const closeVideoModal = () => {
      showVideoModal.value = false;
      selectedTemplate.value = null;
    };
    const closeVideoModalOnly = () => {
      showVideoModal.value = false;
    };
    const switchPrimaryTab = (index) => {
      activePrimaryTab.value = index;
      selectedTemplate.value = null;
      currentPage.value = 1;
      hasMoreData.value = true;
      loadTemplateList(1);
    };
    const selectTemplate = (template) => {
      selectedTemplate.value = template;
      common_vendor.index.__f__("log", "at pages/my/video-create.vue:418", "选择模板:", template);
    };
    const confirmTemplateSelection = () => {
      common_vendor.index.__f__("log", "at pages/my/video-create.vue:422", selectedTemplate.value);
      if (selectedTemplate.value) {
        common_vendor.index.__f__("log", "at pages/my/video-create.vue:424", "确认选择模板:", selectedTemplate.value);
        currentAvatar.value = getTemplateImage(selectedTemplate.value);
        common_vendor.index.showToast({
          title: `切换成功`,
          icon: "success",
          duration: 2e3
        });
        closeVideoModalOnly();
      } else {
        common_vendor.index.showToast({
          title: "请先选择一个模板",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const onSubtitleChange = () => {
      showSubtitle.value = !showSubtitle.value;
    };
    const closeVoiceModal = () => {
      showVoiceModal.value = false;
      selectedVoice.value = null;
    };
    const switchVoiceTab = (index) => {
      activeVoiceTab.value = index;
    };
    const selectVoice = (voice) => {
      selectedVoice.value = voice;
    };
    const confirmVoiceSelection = () => {
      if (selectedVoice.value) {
        currentVoiceText.value = selectedVoice.value.name;
        common_vendor.index.showToast({
          title: `已选择：${selectedVoice.value.name}`,
          icon: "success",
          duration: 2e3
        });
        closeVoiceModal();
      } else {
        common_vendor.index.showToast({
          title: "请先选择一个配音",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const getPrivacyContent = async () => {
      try {
        const res = await api_index.userVoicePrivacyApi();
        privacyContent.value = res.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/my/video-create.vue:511", "获取隐私协议失败:", error);
        privacyContent.value = '<p style="color: #ff6b6b;text-align: center;">获取隐私协议内容失败，请稍后重试。</p>';
      }
    };
    const startCountdown = () => {
      canConfirm.value = false;
      countdown.value = 3;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
          canConfirm.value = true;
        }
      }, 1e3);
    };
    const handleGenerateVideo = async () => {
      var _a;
      common_vendor.index.__f__("log", "at pages/my/video-create.vue:532", selectedTemplate.value);
      if (!selectedTemplate.value) {
        common_vendor.index.showToast({
          title: "请先选择模板",
          icon: "none"
        });
        return;
      }
      if (!textContent.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入文本内容",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "检查算力中...",
          mask: true
        });
        const calculateParams = {
          merchantGuid: userStore.merchantGuid,
          text: textContent.value,
          audioType: "tts"
          // 默认音频类型
        };
        if (activePrimaryTab.value === 0) {
          calculateParams.personId = selectedTemplate.value.id;
          calculateParams.audioManId = selectedTemplate.value.audioManId;
          calculateParams.figureType = (_a = selectedTemplate.value.figures[0]) == null ? void 0 : _a.type;
        } else {
          calculateParams.personId = selectedTemplate.value.chanjingPersonId;
          calculateParams.audioManId = selectedTemplate.value.audioManId;
          calculateParams.figureType = "";
        }
        const [userInfoRes, calculateRes] = await Promise.all([
          api_index.getUserInfoApi(),
          api_index.calculatePointsApi(calculateParams)
        ]);
        common_vendor.index.hideLoading();
        const userPoints = userInfoRes.data.chat_count || 0;
        const requiredPoints = calculateRes.data.requiredPoints || 0;
        if (userPoints < requiredPoints) {
          common_vendor.index.showModal({
            title: "算力不足",
            content: `当前算力：${userPoints}，所需算力：${requiredPoints}，请先充值算力`,
            showCancel: false,
            confirmText: "去充值",
            success: (res) => {
              if (res.confirm) {
              }
            }
          });
          return;
        }
        showPrivacyModal.value = true;
        await getPrivacyContent();
        startCountdown();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/my/video-create.vue:606", "检查算力失败:", error);
        common_vendor.index.showToast({
          title: "检查失败，请重试",
          icon: "none"
        });
      }
    };
    const confirmPrivacy = async () => {
      var _a, _b;
      if (!canConfirm.value)
        return;
      showPrivacyModal.value = false;
      try {
        common_vendor.index.showToast({
          title: "创建中...",
          icon: "loading",
          duration: 0,
          // 持续显示直到手动隐藏
          mask: true
        });
        const createParams = {
          merchantGuid: userStore.merchantGuid,
          text: textContent.value,
          audioType: "tts",
          // 默认音频类型
          subtitleShow: showSubtitle.value
        };
        let previewUrl = "";
        if (activePrimaryTab.value === 0) {
          createParams.personId = selectedTemplate.value.id;
          createParams.audioManId = selectedTemplate.value.audioManId;
          createParams.figureType = (_a = selectedTemplate.value.figures[0]) == null ? void 0 : _a.type;
          previewUrl = (_b = selectedTemplate.value.figures[0]) == null ? void 0 : _b.cover;
        } else {
          createParams.personId = selectedTemplate.value.chanjingPersonId;
          createParams.audioManId = selectedTemplate.value.audioManId;
          createParams.figureType = "";
          previewUrl = selectedTemplate.value.picUrl;
        }
        common_vendor.index.__f__("log", "at pages/my/video-create.vue:651", previewUrl);
        const imageInfo = await getImageInfo(previewUrl);
        if (imageInfo) {
          createParams.personWidth = imageInfo.width;
          createParams.personHeight = imageInfo.height;
        }
        const res = await api_index.createVideoTaskApi(createParams);
        common_vendor.index.hideToast();
        if (res.code === 0) {
          common_vendor.index.showToast({
            title: "视频任务创建成功",
            icon: "success"
          });
          common_vendor.index.navigateTo({
            url: `/pages/my/video-progress?orderNo=${res.data.orderNo}&previewUrl=${previewUrl}`
          });
        } else {
          common_vendor.index.showToast({
            title: res.msg || "创建失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.hideToast();
        common_vendor.index.__f__("error", "at pages/my/video-create.vue:684", "创建视频任务失败:", error);
        common_vendor.index.showToast({
          title: error.meg || "创建失败，请重试",
          icon: "none"
        });
      }
    };
    const closePrivacyModal = () => {
      showPrivacyModal.value = false;
    };
    common_vendor.onMounted(() => {
      currentPage.value = 1;
      hasMoreData.value = true;
      activePrimaryTab.value = 0;
      loadTemplateList(1, false, true);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: currentAvatar.value,
        b: showSubtitle.value
      }, showSubtitle.value ? {} : {}, {
        c: common_assets._imports_0$5,
        d: common_vendor.o(openVideoModal),
        e: showSubtitle.value ? "/static/my/select-icon1.png" : "/static/my/select-icon2.png",
        f: common_vendor.o(onSubtitleChange),
        g: textContent.value,
        h: common_vendor.o(($event) => textContent.value = $event.detail.value),
        i: common_vendor.o(handleGenerateVideo),
        j: showVideoModal.value
      }, showVideoModal.value ? common_vendor.e({
        k: common_vendor.f(primaryTabs.value, (primaryTab, index, i0) => {
          return {
            a: common_vendor.t(primaryTab),
            b: index,
            c: activePrimaryTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchPrimaryTab(index), index)
          };
        }),
        l: common_assets._imports_1$3,
        m: common_vendor.o(confirmTemplateSelection),
        n: templateList.value.length === 0 && currentPage.value === 1 && !isLoadingMore.value
      }, templateList.value.length === 0 && currentPage.value === 1 && !isLoadingMore.value ? {} : {
        o: common_vendor.f(templateList.value, (template, index, i0) => {
          return {
            a: getTemplateImage(template),
            b: index,
            c: selectedTemplate.value && selectedTemplate.value.id === template.id ? 1 : "",
            d: common_vendor.o(($event) => selectTemplate(template), index)
          };
        })
      }, {
        p: templateList.value.length > 0
      }, templateList.value.length > 0 ? common_vendor.e({
        q: isLoadingMore.value
      }, isLoadingMore.value ? {} : hasMoreData.value ? {} : {}, {
        r: hasMoreData.value
      }) : {}, {
        s: common_vendor.o(loadMoreTemplates),
        t: common_assets._imports_2$1,
        v: common_vendor.o(closeVideoModal),
        w: common_vendor.o(() => {
        }),
        x: common_vendor.o(closeVideoModal)
      }) : {}, {
        y: showVoiceModal.value
      }, showVoiceModal.value ? {
        z: common_assets._imports_1$3,
        A: common_vendor.o(confirmVoiceSelection),
        B: common_vendor.f(voiceTabs.value, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab),
            b: index,
            c: activeVoiceTab.value === index ? 1 : "",
            d: common_vendor.o(($event) => switchVoiceTab(index), index)
          };
        }),
        C: common_vendor.f(currentVoices.value, (voice, index, i0) => {
          return {
            a: common_vendor.t(voice.name),
            b: index,
            c: selectedVoice.value && selectedVoice.value.id === voice.id ? 1 : "",
            d: common_vendor.o(($event) => selectVoice(voice), index)
          };
        }),
        D: common_vendor.o(() => {
        }),
        E: common_vendor.o(closeVoiceModal)
      } : {}, {
        F: showPrivacyModal.value
      }, showPrivacyModal.value ? {
        G: privacyContent.value,
        H: common_vendor.t(canConfirm.value ? "我同意" : `我同意(${countdown.value}s)`),
        I: !canConfirm.value ? 1 : "",
        J: common_vendor.o(confirmPrivacy),
        K: common_vendor.o(() => {
        }),
        L: common_vendor.o(closePrivacyModal)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-979d1c47"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/my/video-create.js.map
